<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Musician Monthly Plan
        SubscriptionPlan::create([
            'name' => 'Musician Monthly',
            'slug' => 'musician-monthly',
            'user_type' => 'musician',
            'price' => 10.95,
            'billing_cycle' => 'monthly',
            'trial_days' => 30,
            'description' => 'Monthly subscription for musicians with 30 days free trial',
            'features' => [
                'Create your listing',
                'Be present on the "Explore" page with map',
                'Search filters visibility',
                'Avatar/Picture display',
                'Description/Expertise details',
                'Music categories',
                'Played instruments details',
                'Contact information',
                'Image gallery',
                'Contact form',
                'Live Chat box',
                'Share button option'
            ],
            'is_active' => true,
            'is_default' => true,
        ]);

        // Musician Annual Plan
        SubscriptionPlan::create([
            'name' => 'Musician Annual',
            'slug' => 'musician-annual',
            'user_type' => 'musician',
            'price' => 179.95,
            'billing_cycle' => 'yearly',
            'trial_days' => 30,
            'description' => 'Annual subscription for musicians with 30 days free trial',
            'features' => [
                'All Monthly Plan Features',
                'Save over 25% compared to monthly billing',
                'Priority listing in search results'
            ],
            'is_active' => true,
            'is_default' => false,
        ]);

        // Client One-Time Payment
        SubscriptionPlan::create([
            'name' => 'Client Lifetime',
            'slug' => 'client-lifetime',
            'user_type' => 'client',
            'price' => 4.99,
            'billing_cycle' => 'one_time',
            'trial_days' => 0,
            'description' => 'One-time payment for clients to access premium features',
            'features' => [
                'Compare musicians',
                'Add to wishlist',
                'Follow musicians',
                'Leave reviews',
                'Live chat with musicians',
                'Contact form access'
            ],
            'is_active' => true,
            'is_default' => true,
        ]);
    }
}

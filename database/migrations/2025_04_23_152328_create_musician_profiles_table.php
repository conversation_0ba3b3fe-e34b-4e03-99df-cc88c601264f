<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('musician_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('country_code',20)->nullable();
            $table->string('phone_number')->nullable();
            $table->string('website')->nullable();
            $table->text('description')->nullable();
            $table->string('header_image')->nullable();
            $table->json('roles')->nullable(); // ['musician', 'dj', 'manager', 'tech_support']
            $table->json('offered_services')->nullable(); // ['performing', 'tutoring', 'booking_management', 'tech_support', 'gig_replacement']
            $table->json('instruments')->nullable(); // Array of instruments
            $table->json('music_types')->nullable(); // Array of music genres
            $table->json('spoken_languages')->nullable(); // Array of languages
            $table->json('payment_methods')->nullable(); // ['debit_card', 'paypal', 'crypto', 'bankwire']
            $table->decimal('rate_per_hour', 10, 2)->nullable();
            $table->decimal('rate_per_event', 10, 2)->nullable();
            $table->json('social_links')->nullable(); // ['instagram' => 'https://instagram.com/username']
            $table->json('tags')->nullable(); // Array of keywords/tags
            $table->string('location')->nullable();
            $table->decimal('average_rating', 10, 2)->nullable();
            $table->integer('ratings_count')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('musician_profiles');
    }
};

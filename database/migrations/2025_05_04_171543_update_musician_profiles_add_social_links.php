<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First add the column
        Schema::table('musician_profiles', function (Blueprint $table) {
            // Add new social_links JSON column
            $table->json('social_links')->nullable()->after('instagram_link');
        });

        // Then update the data in a separate statement
        DB::statement('UPDATE musician_profiles SET social_links = JSON_OBJECT("instagram", instagram_link) WHERE instagram_link IS NOT NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('musician_profiles', function (Blueprint $table) {
            // Drop the social_links column
            $table->dropColumn('social_links');
        });
    }
};

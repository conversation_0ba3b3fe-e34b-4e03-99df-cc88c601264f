<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Services\NotificationService;
use Livewire\Component;
use Livewire\WithPagination;

class NotificationSender extends Component
{
    use WithPagination;

    // Form properties
    public $title = '';
    public $body = '';
    public $targetType = 'all'; // 'all', 'group', 'specific'
    public $userGroup = 'all'; // 'all', 'musician', 'client'
    
    // User selection
    public $selectAll = false;
    public $selectedUsers = [];
    
    // Search and filter
    public $search = '';
    public $roleFilter = '';
    
    // Status
    public $isSending = false;
    public $result = null;

    public $userCount;
    
    protected $rules = [
        'title' => 'required|string|max:255',
        'body' => 'required|string',
        'targetType' => 'required|in:all,group,specific',
        'userGroup' => 'required_if:targetType,group|in:all,musician,client',
        'selectedUsers' => 'required_if:targetType,specific|array',
    ];

    protected $messages = [
        'selectedUsers.required_if' => 'Please select at least one user.',
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedRoleFilter()
    {
        $this->resetPage();
    }

    public function updatedTargetType()
    {
        $this->selectedUsers = [];
        $this->selectAll = false;
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedUsers = $this->getFilteredUsers()->pluck('id')->map(fn($id) => (string) $id)->toArray();
        } else {
            $this->selectedUsers = [];
        }
    }

    public function getFilteredUsers()
    {
        return User::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->roleFilter, function ($query) {
                $query->where('role', $this->roleFilter);
            })
            ->orderBy('name')
            ->get();
    }

    public function getUsersProperty()
    {
        return $this->getFilteredUsers();
    }

    public function getUserCountProperty()
    {
        if ($this->targetType === 'all') {
            return User::count();
        } elseif ($this->targetType === 'group') {
            if ($this->userGroup === 'all') {
                return User::count();
            } else {
                return User::where('role', $this->userGroup)->count();
            }
        } else {
            return count($this->selectedUsers);
        }
    }

    public function sendNotification()
    {
        $this->validate();
        
        $this->isSending = true;
        
        $notificationService = app(NotificationService::class);
        
        if ($this->targetType === 'specific') {
            // Send to specific users
            $this->result = $notificationService->sendNotification(
                $this->title,
                $this->body,
                $this->selectedUsers,
                null
            );
        } else {
            // Send to all or a group
            $notificationFor = $this->targetType === 'all' ? 'all' : $this->userGroup;
            $this->result = $notificationService->sendNotification(
                $this->title,
                $this->body,
                null,
                $notificationFor
            );
        }
        
        $this->isSending = false;
        
        if ($this->result['success']) {
            $this->dispatch(
                'notify',
                message: $this->result['message'],
                type: 'success',
            );
            $this->reset(['title', 'body', 'selectedUsers', 'selectAll']);
            $this->targetType = 'all';
            $this->userGroup = 'all';
        } else {
            $this->dispatch(
                'notify',
                message: $this->result['message'],
                type: 'error',
            );
        }
    }

    public function render()
    {
        return view('livewire.admin.notification-sender', [
            'users' => $this->targetType === 'specific' ? $this->users : [],
        ]);
    }
}

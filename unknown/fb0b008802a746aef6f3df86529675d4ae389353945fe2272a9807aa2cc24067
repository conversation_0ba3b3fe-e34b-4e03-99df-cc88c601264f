<?php

namespace App\Livewire\Admin;

use App\Models\SubscriptionPlan;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithPagination;

class SubscriptionPlanManagement extends Component
{
    use WithPagination;

    // Search and filter properties
    public $search = '';
    public $userTypeFilter = '';
    public $billingCycleFilter = '';
    
    // Modal control properties
    public $isModalOpen = false;
    public $isDeleteModalOpen = false;
    public $modalMode = 'create'; // 'create' or 'edit'
    
    // Form properties
    public $selectedPlan = null;
    public $planId = null;
    public $name = '';
    public $slug = '';
    public $user_type = 'musician';
    public $price = 0;
    public $billing_cycle = 'monthly';
    public $trial_days = 0;
    public $description = '';
    public $features = [];
    public $is_active = true;
    public $is_default = false;
    
    // Feature management
    public $newFeature = '';
    
    protected $rules = [
        'name' => 'required|string|max:255',
        'user_type' => 'required|in:musician,client',
        'price' => 'required|numeric|min:0',
        'billing_cycle' => 'required|in:one_time,monthly,yearly',
        'trial_days' => 'required|integer|min:0',
        'description' => 'nullable|string',
        'features' => 'nullable|array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedUserTypeFilter()
    {
        $this->resetPage();
    }

    public function updatedBillingCycleFilter()
    {
        $this->resetPage();
    }

    public function getPlans()
    {
        return SubscriptionPlan::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->userTypeFilter, function ($query) {
                $query->where('user_type', $this->userTypeFilter);
            })
            ->when($this->billingCycleFilter, function ($query) {
                $query->where('billing_cycle', $this->billingCycleFilter);
            })
            ->latest()
            ->paginate(10);
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->modalMode = 'create';
        $this->isModalOpen = true;
    }

    public function openEditModal($planId)
    {
        $this->resetForm();
        $this->modalMode = 'edit';
        $this->selectedPlan = SubscriptionPlan::findOrFail($planId);
        $this->planId = $this->selectedPlan->id;
        $this->name = $this->selectedPlan->name;
        $this->slug = $this->selectedPlan->slug;
        $this->user_type = $this->selectedPlan->user_type;
        $this->price = $this->selectedPlan->price;
        $this->billing_cycle = $this->selectedPlan->billing_cycle;
        $this->trial_days = $this->selectedPlan->trial_days;
        $this->description = $this->selectedPlan->description;
        $this->features = $this->selectedPlan->features ?? [];
        $this->is_active = $this->selectedPlan->is_active;
        $this->is_default = $this->selectedPlan->is_default;
        
        $this->isModalOpen = true;
    }

    public function confirmDelete($planId)
    {
        $this->selectedPlan = SubscriptionPlan::findOrFail($planId);
        $this->isDeleteModalOpen = true;
    }

    public function deletePlan()
    {
        if ($this->selectedPlan) {
            // Check if plan has any subscriptions
            if ($this->selectedPlan->subscriptions()->count() > 0) {
                $this->dispatch(
                    'notify',
                    message: 'Cannot delete plan with active subscriptions',
                    type: 'error',
                );
                $this->isDeleteModalOpen = false;
                return;
            }
            
            $this->selectedPlan->delete();
            $this->isDeleteModalOpen = false;
            $this->dispatch(
                'notify',
                message: 'Subscription plan deleted successfully',
                type: 'success',
            );
        }
    }

    public function savePlan()
    {
        $this->validate();
        
        // Generate slug if empty
        if (empty($this->slug)) {
            $this->slug = Str::slug($this->name);
        }
        
        // Check if slug is unique
        $query = SubscriptionPlan::where('slug', $this->slug);
        if ($this->modalMode === 'edit') {
            $query->where('id', '!=', $this->planId);
        }
        
        if ($query->exists()) {
            $this->slug = $this->slug . '-' . Str::random(5);
        }
        
        // If setting as default, unset other defaults for this user type
        if ($this->is_default) {
            SubscriptionPlan::where('user_type', $this->user_type)
                ->where('is_default', true)
                ->when($this->modalMode === 'edit', function ($query) {
                    $query->where('id', '!=', $this->planId);
                })
                ->update(['is_default' => false]);
        }
        
        $data = [
            'name' => $this->name,
            'slug' => $this->slug,
            'user_type' => $this->user_type,
            'price' => $this->price,
            'billing_cycle' => $this->billing_cycle,
            'trial_days' => $this->trial_days,
            'description' => $this->description,
            'features' => $this->features,
            'is_active' => $this->is_active,
            'is_default' => $this->is_default,
        ];
        
        if ($this->modalMode === 'create') {
            SubscriptionPlan::create($data);
            $message = 'Subscription plan created successfully';
        } else {
            $this->selectedPlan->update($data);
            $message = 'Subscription plan updated successfully';
        }
        
        $this->isModalOpen = false;
        $this->dispatch(
            'notify',
            message: $message,
            type: 'success',
        );
    }

    public function addFeature()
    {
        if (!empty($this->newFeature)) {
            $this->features[] = $this->newFeature;
            $this->newFeature = '';
        }
    }

    public function removeFeature($index)
    {
        if (isset($this->features[$index])) {
            unset($this->features[$index]);
            $this->features = array_values($this->features); // Re-index array
        }
    }

    public function resetForm()
    {
        $this->selectedPlan = null;
        $this->planId = null;
        $this->name = '';
        $this->slug = '';
        $this->user_type = 'musician';
        $this->price = 0;
        $this->billing_cycle = 'monthly';
        $this->trial_days = 0;
        $this->description = '';
        $this->features = [];
        $this->is_active = true;
        $this->is_default = false;
        $this->newFeature = '';
        
        $this->resetErrorBag();
    }

    public function render()
    {
        return view('livewire.admin.subscription-plan-management', [
            'plans' => $this->getPlans(),
        ]);
    }
}

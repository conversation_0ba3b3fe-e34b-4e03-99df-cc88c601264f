{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"autoprefixer": "^10.4.20", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "vite": "^6.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.10", "laravel-echo": "^2.0.2", "pusher-js": "^8.4.0", "tailwindcss": "^4.1.10"}}
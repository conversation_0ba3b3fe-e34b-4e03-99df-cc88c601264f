import Echo from 'laravel-echo';

import Pusher from 'pusher-js';
window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT ?? 80,
    wssPort: import.meta.env.VITE_REVERB_PORT ?? 443,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});



window.Echo.connector.pusher.connection.bind('connecting', (payload) => {
    console.log('Websocket connecting...');
});
window.Echo.connector.pusher.connection.bind('connected', (payload) => {
    console.log('Websocket connected.', payload);
});
window.Echo.connector.pusher.connection.bind('unavailable', (payload) => {
    console.log('Websocket unavailable.', payload);
});
window.Echo.connector.pusher.connection.bind('failed', (payload) => {
    console.log('Websocket failed.', payload);
});
window.Echo.connector.pusher.connection.bind('disconnected', (payload) => {
    console.log('Websocket disconnected.', payload);
});
window.Echo.connector.pusher.connection.bind('message', (payload) => {
    console.log('Message received.', payload);
});
window.Echo.channel("user.25").listen("UnreadCountUpdated", (event) => {
    console.log("Received event:", event);
});
window.Echo.channel("user.25").listen("UnreadNotificationsCountUpdated", (event) => {
    console.log("Received event:", event);
});

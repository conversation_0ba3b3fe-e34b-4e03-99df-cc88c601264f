<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">

<head>
    @include('partials.head')
</head>

<body class="min-h-screen bg-white dark:bg-zinc-800">
    <flux:sidebar sticky stashable class="border-r border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900">
        <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

        <a href="{{ route('dashboard') }}" class="me-5 flex items-center justify-center space-x-2 rtl:space-x-reverse"
            wire:navigate>
            <x-app-logo />
        </a>

        <flux:navlist variant="outline">
            <flux:navlist.group :heading="__('Platform')" class="grid">
                <flux:navlist.item icon="home" :href="route('dashboard')" :current="request()->routeIs('dashboard')"
                    wire:navigate>{{ __('Dashboard') }}</flux:navlist.item>

                <flux:navlist.item class="mt-3" icon="users" :href="route('users-management')"
                    :current="request()->routeIs('users-management')" wire:navigate>{{ __('Users Management') }}
                </flux:navlist.item>

                <flux:navlist.item class="mt-3" icon="calendar-days" :href="route('subscription-management')"
                    :current="request()->routeIs('subscription-management')" wire:navigate>
                    {{ __('User Subscriptions') }}</flux:navlist.item>

                <flux:navlist.item class="mt-3" icon="credit-card" :href="route('subscription-plans-management')"
                    :current="request()->routeIs('subscription-plans-management')" wire:navigate>
                    {{ __('Subscription Plans') }}</flux:navlist.item>



                <flux:navlist.item class="mt-3" icon="photo" :href="route('promotional-banners-management')"
                    :current="request()->routeIs('promotional-banners-management')" wire:navigate>
                    {{ __('Promotional Banners') }}</flux:navlist.item>

                <flux:navlist.group :heading="__('Notifications')" class="mt-3">
                    <flux:navlist.item icon="paper-airplane" :href="route('notification-sender')"
                        :current="request()->routeIs('notification-sender')" wire:navigate>
                        {{ __('Send Notifications') }}</flux:navlist.item>
                    <flux:navlist.item icon="bell" :href="route('notification-management')"
                        :current="request()->routeIs('notification-management')" wire:navigate>
                        {{ __('Manage Notifications') }}</flux:navlist.item>
                </flux:navlist.group>

                <flux:navlist.group :heading="__('Pages')" class="mt-3">
                    <flux:navlist.item icon="document-text" :href="route('admin.pages.index')"
                        :current="request()->routeIs('admin.pages.index')" wire:navigate>{{ __('Manage Pages') }}
                    </flux:navlist.item>
                </flux:navlist.group>
            </flux:navlist.group>
        </flux:navlist>

        <flux:spacer />

        <!-- Desktop User Menu -->
        <flux:dropdown position="bottom" align="start">
            <flux:profile :name="auth('admin')->user()->name" :initials="auth('admin')->user()->initials()"
                icon-trailing="chevrons-up-down" />

            <flux:menu class="w-[220px]">
                <flux:menu.radio.group>
                    <div class="p-0 text-sm font-normal">
                        <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                            <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                <span
                                    class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                                    {{ auth('admin')->user()->initials() }}
                                </span>
                            </span>

                            <div class="grid flex-1 text-start text-sm leading-tight">
                                <span class="truncate font-semibold">{{ auth('admin')->user()->name }}</span>
                                <span class="truncate text-xs">{{ auth('admin')->user()->email }}</span>
                            </div>
                        </div>
                    </div>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <flux:menu.radio.group>
                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>
                        {{ __('Settings') }}</flux:menu.item>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <form method="POST" action="{{ route('logout') }}" class="w-full">
                    @csrf
                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                        {{ __('Log Out') }}
                    </flux:menu.item>
                </form>
            </flux:menu>
        </flux:dropdown>
    </flux:sidebar>

    <!-- Mobile User Menu -->
    <flux:header class="lg:hidden">
        <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

        <flux:spacer />

        <flux:dropdown position="top" align="end">
            <flux:profile :initials="auth('admin')->user()->initials()" icon-trailing="chevron-down" />

            <flux:menu>
                <flux:menu.radio.group>
                    <div class="p-0 text-sm font-normal">
                        <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                            <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                <span
                                    class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white">
                                    {{ auth('admin')->user()->initials() }}
                                </span>
                            </span>

                            <div class="grid flex-1 text-start text-sm leading-tight">
                                <span class="truncate font-semibold">{{ auth('admin')->user()->name }}</span>
                                <span class="truncate text-xs">{{ auth('admin')->user()->email }}</span>
                            </div>
                        </div>
                    </div>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <flux:menu.radio.group>
                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>
                        {{ __('Settings') }}</flux:menu.item>
                </flux:menu.radio.group>

                <flux:menu.separator />

                <form method="POST" action="{{ route('logout') }}" class="w-full">
                    @csrf
                    <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                        {{ __('Log Out') }}
                    </flux:menu.item>
                </form>
            </flux:menu>
        </flux:dropdown>
    </flux:header>

    <livewire:notification />

    {{ $slot }}

    @fluxScripts
    @stack('scripts')
</body>

</html>

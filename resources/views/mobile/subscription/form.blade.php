@extends('mobile.layouts.app')

@section('title', 'Subscribe to ' . $plan->name)

@section('header', 'Subscribe to ' . $plan->name)

@section('content')
    <div class="mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-white">Plan Details</h2>
            <div class="px-3 py-1 rounded-full bg-red-500 text-white text-sm font-medium">
                {{ ucfirst($plan->user_type) }}
            </div>
        </div>

        <div class="bg-gray-700 rounded-lg p-4 mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <p class="text-2xl font-bold text-white">
                        ${{ number_format($plan->price, 2) }}
                    </p>
                    <p class="text-gray-300">
                        @if ($plan->billing_cycle == 'monthly')
                            Monthly billing
                        @elseif($plan->billing_cycle == 'yearly')
                            Annual billing
                        @else
                            One-time payment
                        @endif
                    </p>
                </div>

                @if ($plan->trial_days > 0)
                    <div class="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm font-medium">
                        {{ $plan->trial_days }} days free trial
                    </div>
                @endif
            </div>

            @if ($plan->description)
                <p class="mt-3 text-gray-300 text-sm">{{ $plan->description }}</p>
            @endif
        </div>
    </div>

    <form id="payment-form"
        action="{{ route('mobile.process-subscription', ['token' => request()->token, 'planId' => $plan->id]) }}"
        method="POST">
        @csrf
        <div class="mb-5">
            <label for="card-element" class="block text-sm font-medium text-gray-300 mb-2">
                Credit or debit card
            </label>
            <div id="card-element" class="p-3 bg-gray-700 border border-gray-600 rounded-lg">
                <!-- Stripe Card Element will be inserted here -->
            </div>
            <div id="card-errors" class="mt-2 text-red-400 text-sm" role="alert"></div>
        </div>

        <input type="hidden" id="payment_method_id" name="payment_method_id">

        <div class="mt-6">
            <button id="submit-button" type="submit"
                class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors font-medium">
                Subscribe Now
            </button>
        </div>
    </form>
@endsection

@section('scripts')
    <script>
        // Create a Stripe client
        const stripe = Stripe('{{ $stripeKey }}');
        const elements = stripe.elements();

        // Create an instance of the card Element
        const cardElement = elements.create('card', {
            hidePostalCode: true, // 👈 Hides postal/ZIP code
            style: {
                base: {
                    color: '#fff',
                    fontFamily: 'Inter, sans-serif',
                    fontSmoothing: 'antialiased',
                    fontSize: '16px',
                    '::placeholder': {
                        color: '#9ca3af'
                    },
                    backgroundColor: '#374151',
                },
                invalid: {
                    color: '#f87171',
                    iconColor: '#f87171'
                }
            }
        });

        // Add an instance of the card Element into the `card-element` div
        cardElement.mount('#card-element');

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Disable the submit button to prevent repeated clicks
            submitButton.disabled = true;
            submitButton.innerHTML =
                '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Processing...';

            try {
                const {
                    paymentMethod,
                    error
                } = await stripe.createPaymentMethod({
                    type: 'card',
                    card: cardElement,
                });

                if (error) {
                    // Show error to your customer
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = error.message;
                    submitButton.disabled = false;
                    submitButton.textContent = 'Subscribe Now';
                } else {
                    // Send paymentMethod.id to your server
                    document.getElementById('payment_method_id').value = paymentMethod.id;
                    form.submit();
                }
            } catch (err) {
                console.error(err);
                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = 'An unexpected error occurred. Please try again.';
                submitButton.disabled = false;
                submitButton.textContent = 'Subscribe Now';
            }
        });
    </script>
@endsection

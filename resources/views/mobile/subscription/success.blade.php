@extends('mobile.layouts.app')

@section('title', 'Subscription Successful')

@section('header', 'Subscription Successful!')

@section('content')
    <div class="mb-6 text-center">
        <div class="bg-green-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>
        <h2 class="text-lg font-semibold text-white">Thank you for your subscription!</h2>
        <p class="mt-2 text-gray-300">Your subscription to {{ $subscription->plan->name }} has been activated.</p>
    </div>

    <div class="bg-gray-700 rounded-lg p-5 mb-6">
        <h3 class="font-medium text-white mb-3">Subscription Details</h3>
        <div class="space-y-3 text-gray-300">
            <div class="flex justify-between">
                <span class="text-gray-400">Plan</span>
                <span class="font-medium">{{ $subscription->plan->name }}</span>
            </div>

            <div class="flex justify-between">
                <span class="text-gray-400">Price</span>
                <span class="font-medium">${{ number_format($subscription->plan->price, 2) }}
                    @if ($subscription->plan->billing_cycle == 'monthly')
                        / month
                    @elseif($subscription->plan->billing_cycle == 'yearly')
                        / year
                    @else
                        (one-time)
                    @endif
                </span>
            </div>

            <div class="flex justify-between">
                <span class="text-gray-400">Status</span>
                <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    @if ($subscription->status == 'active') bg-green-500 text-white
                    @elseif($subscription->status == 'trialing')
                        bg-blue-500 text-white
                    @else
                        bg-gray-500 text-white @endif
                ">
                    {{ ucfirst($subscription->status) }}
                </span>
            </div>

            @if ($subscription->trial_ends_at)
                <div class="flex justify-between">
                    <span class="text-gray-400">Trial Ends</span>
                    <span class="font-medium">{{ $subscription->trial_ends_at->format('M j, Y') }}</span>
                </div>
            @endif

            @if ($subscription->next_billing_date && $subscription->plan->billing_cycle != 'one_time')
                <div class="flex justify-between">
                    <span class="text-gray-400">Next Billing</span>
                    <span class="font-medium">{{ $subscription->next_billing_date->format('M j, Y') }}</span>
                </div>
            @endif
        </div>
    </div>

    <div class="text-center">
        <p class="text-gray-400 text-sm mb-4">You can now close this window and return to the app.</p>
        <a href="/handle-app-return-success"
            class="w-full block bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors font-medium">
            Return to App
        </a>
    </div>
@endsection

@extends('mobile.layouts.app')

@section('title', 'Subscription Canceled')

@section('header', 'Subscription Canceled')

@section('content')
    <div class="mb-6 text-center">
        <div class="bg-yellow-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <h2 class="text-lg font-semibold text-white">Your subscription has been canceled</h2>
        <p class="mt-2 text-gray-300">{{ $message }}</p>
    </div>

    @if(isset($subscription) && $subscription->ends_at)
        <div class="bg-gray-700 rounded-lg p-5 mb-6">
            <h3 class="font-medium text-white mb-3">Subscription Details</h3>
            <div class="space-y-3 text-gray-300">
                <div class="flex justify-between">
                    <span class="text-gray-400">Plan</span>
                    <span class="font-medium">{{ $subscription->plan->name }}</span>
                </div>

                <div class="flex justify-between">
                    <span class="text-gray-400">Access Until</span>
                    <span class="font-medium">{{ $subscription->ends_at->format('M j, Y') }}</span>
                </div>
            </div>
        </div>
    @endif

    <div class="text-center">
        <p class="text-gray-400 text-sm mb-4">You can now close this window and return to the app.</p>
        <a href="/handle-app-return" class="w-full block bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors font-medium">
            Return to App
        </a>
    </div>
@endsection

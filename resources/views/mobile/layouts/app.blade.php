<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name') }} - @yield('title', 'Subscription')</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Stripe JS -->
    <script src="https://js.stripe.com/v3/"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>
    
    @yield('styles')
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <div class="max-w-md mx-auto py-8 px-4">
        <div class="text-center mb-6">
            <img src="{{ asset('assets/images/logo.png') }}" alt="{{ config('app.name') }}" class="h-20 mx-auto">
        </div>
        
        <div class="bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div class="bg-blue-600 p-4">
                <h1 class="text-xl font-bold text-center">@yield('header', 'Subscription')</h1>
            </div>
            
            <div class="p-6">
                @yield('content')
            </div>
        </div>
        
        <div class="mt-6 text-center text-gray-400 text-sm">
            &copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
        </div>
    </div>
    
    @yield('scripts')
</body>
</html>

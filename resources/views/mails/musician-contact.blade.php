<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>You've Got a Message - {{ env('APP_NAME') }}</title>
    <style>
        @media only screen and (max-width: 600px) {
            .main {
                width: 320px !important;
            }

            table[class="contenttable"] {
                width: 320px !important;
                text-align: left !important;
            }

            td[class="force-col"] {
                display: block !important;
            }

            td[class="rm-col"] {
                display: none !important;
            }

            .mt {
                margin-top: 15px !important;
            }

            *[class].width300 {
                width: 255px !important;
            }

            *[class].block {
                display: block !important;
            }

            *[class].blockcol {
                display: none !important;
            }
        }
    </style>
</head>

<body link="#000000" vlink="#000000" alink="#000000">
    <table class="main contenttable" align="center"
        style="font-weight: normal;border-collapse: collapse;border: 0;margin-left: auto;margin-right: auto;padding: 0;font-family: Arial, sans-serif;color: #555559;background-color: white;font-size: 16px;line-height: 26px;width: 600px;margin-top:4rem;">
        <tr>
            <td class="border"
                style="border-collapse: collapse;border: 1px solid #eeeff0;margin: 0;padding: 0;-webkit-text-size-adjust: none;">
                <table
                    style="font-weight: normal;border-collapse: collapse;border: 0;margin: 0;padding: 0;font-family: Arial, sans-serif;border-bottom: 4px solid #0330BF;box-shadow: 0px 10px 10px rgba(0,0,0,0.06);">
                    <tr>
                        <td valign="top" class="image-section"
                            style="padding: 20px; background-color: #fff; border-bottom: 4px solid #0330BF">
                            <img height="70px" src="{{ asset('assets/images/logo.png') }}" alt="logo">
                        </td>
                    </tr>
                    <tr>
                        <td valign="top" class="side title"
                            style="padding: 20px; background-color: white;">
                            <table style="width: 100%;">
                                <tr>
                                    <td class="head-title"
                                        style="color: #333; font-size: 24px; font-weight: bold; text-align: center;">
                                        🎶 Someone is interested in your music!
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px 0;">
                                        <p>Hello {{ $musicianName ?? 'Musician' }},</p>
                                        <p>You’ve received a new message from a visitor on {{ env('APP_NAME') }}. Here are the details:</p>
                                        <table style="width: 100%; margin: 10px 0;">
                                            <tr>
                                                <td style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; font-weight: bold;">Name:</td>
                                                <td style="padding: 8px; background-color: #fff; border: 1px solid #dee2e6;">{{ $contactData['name'] ?? 'No name provided.' }}</td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; font-weight: bold;">Email:</td>
                                                <td style="padding: 8px; background-color: #fff; border: 1px solid #dee2e6;">{{ $contactData['email'] ?? 'No email provided.' }}</td>
                                            </tr>
                                            @if(!empty($contactData['phone']))
                                            <tr>
                                                <td style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; font-weight: bold;">Phone:</td>
                                                <td style="padding: 8px; background-color: #fff; border: 1px solid #dee2e6;">{{ $contactData['phone'] }}</td>
                                            </tr>
                                            @endif
                                            <tr>
                                                <td style="padding: 8px; background-color: #f8f9fa; border: 1px solid #dee2e6; font-weight: bold;">Received At:</td>
                                                <td style="padding: 8px; background-color: #fff; border: 1px solid #dee2e6;">{{ $contactData['submitted_at'] ?? now()->format('d M, Y h:i A') }}</td>
                                            </tr>
                                        </table>

                                        <p><strong>Message:</strong></p>
                                        <div style="background-color: #f1f3f5; padding: 15px; border: 1px solid #ced4da; border-radius: 5px;">
                                            {{ $contactData['message'] ?? 'No message provided.' }}
                                        </div>

                                        <br>
                                        <p style="font-size: 14px; color: #6c757d;">
                                            Please respond directly to this email or reach out to the user via their provided contact details.
                                        </p>
                                        <p style="font-size: 14px; color: #6c757d;">
                                            This message was sent via the musician contact form on {{ env('APP_NAME') }}.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>

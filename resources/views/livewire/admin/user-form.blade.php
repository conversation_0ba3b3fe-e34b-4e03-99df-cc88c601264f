<div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-center justify-between">
        <flux:heading>{{ $userId ? 'Edit User' : 'Add New User' }}</flux:heading>
    </div>

    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm p-6">
        <!-- Tabs -->
        <div class="border-b border-zinc-200 dark:border-zinc-700 mb-6">
            <div class="flex space-x-4">
                <button wire:click="setTab('basic')" type="button"
                    class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'basic' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-zinc-500 hover:text-zinc-700 hover:border-zinc-300 dark:text-zinc-400 dark:hover:text-zinc-300' }}">
                    Basic Information
                </button>
                @if ($role === 'musician')
                    <button wire:click="setTab('musician')" type="button"
                        class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'musician' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-zinc-500 hover:text-zinc-700 hover:border-zinc-300 dark:text-zinc-400 dark:hover:text-zinc-300' }}">
                        Member Profile
                    </button>
                @endif
            </div>
        </div>

        <form wire:submit="save" class="space-y-6" x-data="{ activeTab: @entangle('activeTab') }">
            <!-- Basic Information Tab -->
            <div x-show="activeTab === 'basic'">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div>
                        <flux:label for="name">Name</flux:label>
                        <flux:input id="name" wire:model="name" type="text" placeholder="Full name" />
                        @error('name')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div>
                        <flux:label for="email">Email</flux:label>
                        <flux:input id="email" wire:model="email" type="email" placeholder="<EMAIL>" />
                        @error('email')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div>
                        <flux:label for="password">
                            Password {{ $userId ? '(leave blank to keep current)' : '' }}
                        </flux:label>
                        <flux:input id="password" wire:model="password" type="password" placeholder="Password" />
                        @error('password')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <flux:label for="password_confirmation">Confirm Password</flux:label>
                        <flux:input id="password_confirmation" wire:model="password_confirmation" type="password"
                            placeholder="Confirm password" />
                    </div>

                    <!-- Role -->
                    <div>
                        <flux:label for="role">Role</flux:label>
                        <flux:select id="role" wire:model.live="role">
                            <option value="client">Client</option>
                            <option value="musician">Member</option>
                        </flux:select>
                        @error('role')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Profile Image -->
                    <div>
                        <flux:label for="image">Profile Image</flux:label>
                        <flux:input id="image" wire:model="image" type="file" accept="image/*" />
                        @error('image')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror

                        @if ($image)
                            <div class="mt-3">
                                <div class="text-sm font-medium mb-1">Current Image:</div>
                                <div class="w-40 h-40 overflow-hidden rounded-md">
                                    <img src="{{ $image->temporaryUrl() }}" alt="Profile image"
                                        class="w-full h-full object-cover object-center">
                                </div>
                            </div>
                        @elseif($old_image)
                            <div class="mt-3">
                                <div class="text-sm font-medium mb-1">Current Image:</div>
                                <div class="w-40 h-40 overflow-hidden rounded-md">
                                    <img src="{{ $old_image }}" alt="Profile image"
                                        class="w-full h-full object-cover object-center">
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Status Toggles -->
                    <div class="md:col-span-2">
                        <div class="flex items-center gap-3">
                            <flux:switch id="is_active" wire:model="is_active" />
                            <flux:label for="is_active">Active</flux:label>
                        </div>
                        <div class="flex items-center gap-3 mt-4">
                            <flux:switch id="is_verified" wire:model="is_verified" />
                            <flux:label for="is_verified">Verified</flux:label>
                        </div>
                        @if ($role === 'musician')
                            <div class="flex items-center gap-3 mt-4">
                                <flux:switch id="is_vip_user" wire:model="is_vip_user" />
                                <flux:label for="is_vip_user">VIP User</flux:label>
                            </div>
                            <div class="flex items-center gap-3 mt-4">
                                <flux:switch id="is_live" wire:model="is_live" />
                                <flux:label for="is_live">Is Profile Live</flux:label>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Member Profile Tab -->
            <div x-show="activeTab === 'musician' && $wire.role === 'musician'">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2 grid md:grid-cols-2 gap-6">

                        <!-- Country Code -->
                        <div>
                            <flux:label for="country_code">Country Code</flux:label>
                            <flux:select id="country_code" wire:model="country_code">
                                <option value="">Country Code</option>
                                @foreach ($countryCodes as $countryCode)
                                    <option value="{{ $countryCode['dialCode'] }}">{{ $countryCode['dialCode'] . ' (' . $countryCode['name'] . ' ' . $countryCode['flag'] . ')' }}</option>
                                @endforeach
                            </flux:select>
                            @error('country_code')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                        <!-- Phone Number -->
                        <div>
                            <flux:label for="phone_number">Phone Number</flux:label>
                            <flux:input id="phone_number" wire:model="phone_number" type="text"
                                placeholder="Phone number" />
                            @error('phone_number')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Website -->
                    <div>
                        <flux:label for="website">Website</flux:label>
                        <flux:input id="website" wire:model="website" type="url"
                            placeholder="https://example.com" />
                        @error('website')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <flux:label for="description">Description</flux:label>
                        <flux:textarea id="description" wire:model="description" rows="4"
                            placeholder="Describe yourself as a member..." />
                        @error('description')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Header Image -->
                    <div>
                        <flux:label for="header_image">Header Image</flux:label>
                        <flux:input id="header_image" wire:model="header_image" type="file" accept="image/*" />
                        @error('header_image')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror

                        @if ($header_image)
                            <div class="mt-3">
                                <div class="text-sm font-medium mb-1">Current Image:</div>
                                <div class="w-40 h-40 overflow-hidden rounded-md">
                                    <img src="{{ $header_image->temporaryUrl() }}" alt="Header image"
                                        class="w-full h-full object-cover object-center">
                                </div>
                            </div>
                        @elseif($old_header_image)
                            <div class="mt-3">
                                <div class="text-sm font-medium mb-1">Current Image:</div>
                                <div class="w-40 h-40 overflow-hidden rounded-md">
                                    <img src="{{ $old_header_image }}" alt="Header image"
                                        class="w-full h-full object-cover object-center">
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Social Networks -->
                    <div class="md:col-span-2">
                        <flux:heading size="sm">Social Networks</flux:heading>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                            @foreach ($availableSocialNetworks as $network)
                                <label wire:key="network-{{ $network }}" class="inline-flex items-center">
                                    <input type="checkbox" wire:model.live="selectedSocialNetworks"
                                        value="{{ $network }}"
                                        class="rounded border-zinc-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-primary-600 dark:focus:ring-opacity-25">
                                    <span
                                        class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{ $network }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('selectedSocialNetworks')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror

                        <!-- Social Link Inputs -->
                        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach ($social_links as $index => $socialLink)
                                @if (in_array(ucfirst($socialLink['key'] ?? ''), $selectedSocialNetworks))
                                    <div>
                                        <flux:label for="social_link_{{ $index }}">
                                            {{ ucfirst($socialLink['key']) }} Link</flux:label>
                                        <flux:input id="social_link_{{ $index }}"
                                            wire:model.live="social_links.{{ $index }}.value" type="text"
                                            placeholder="{{ ucfirst($socialLink['key']) }} username or link" />
                                        @error('social_links.{{ $index }}.value')
                                            <span class="text-red-500 text-sm">{{ $message }}</span>
                                        @enderror
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>

                    <!-- Location -->
                    <div>
                        <flux:label for="location">Location</flux:label>
                        <flux:input id="location" wire:model="location" type="text"
                            placeholder="City, Country" />
                        @error('location')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Rate per hour -->
                    <div>
                        <flux:label for="rate_per_hour">Rate per hour ($)</flux:label>
                        <flux:input id="rate_per_hour" wire:model="rate_per_hour" type="number" step="0.01"
                            min="0" placeholder="0.00" />
                        @error('rate_per_hour')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Rate per event -->
                    <div>
                        <flux:label for="rate_per_event">Rate per event ($)</flux:label>
                        <flux:input id="rate_per_event" wire:model="rate_per_event" type="number" step="0.01"
                            min="0" placeholder="0.00" />
                        @error('rate_per_event')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Multi-select fields -->
                    <div class="md:col-span-2">
                        <flux:heading size="sm">Member Roles</flux:heading>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                            @foreach ($availableRoles as $role)
                                <label class="inline-flex items-center">
                                    <input type="checkbox" wire:model="roles" value="{{ $role }}"
                                        class="rounded border-zinc-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-primary-600 dark:focus:ring-opacity-25">
                                    <span
                                        class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{ ucfirst($role) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('roles')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <flux:heading size="sm">Offered Services</flux:heading>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">

                            @foreach ($availableServices as $service)
                                <label class="inline-flex items-center">
                                    <input type="checkbox" wire:model="offered_services" value="{{ $service }}"
                                        class="rounded border-zinc-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-primary-600 dark:focus:ring-opacity-25">
                                    <span
                                        class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{ ucfirst(str_replace('_', ' ', $service)) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('offered_services')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <flux:heading size="sm">Instruments</flux:heading>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">

                            @foreach ($availableInstruments as $instrument)
                                <label class="inline-flex items-center">
                                    <input type="checkbox" wire:model="instruments" value="{{ $instrument }}"
                                        class="rounded border-zinc-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-primary-600 dark:focus:ring-opacity-25">
                                    <span
                                        class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{ ucfirst($instrument) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('instruments')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <flux:heading size="sm">Music Types</flux:heading>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">

                            @foreach ($availableMusicTypes as $type)
                                <label class="inline-flex items-center">
                                    <input type="checkbox" wire:model="music_types" value="{{ $type }}"
                                        class="rounded border-zinc-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-primary-600 dark:focus:ring-opacity-25">
                                    <span
                                        class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{ ucfirst($type) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('music_types')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <flux:heading size="sm">Spoken Languages</flux:heading>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">

                            @foreach ($availableLanguages as $language)
                                <label class="inline-flex items-center">
                                    <input type="checkbox" wire:model="spoken_languages" value="{{ $language }}"
                                        class="rounded border-zinc-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-primary-600 dark:focus:ring-opacity-25">
                                    <span
                                        class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{ ucfirst($language) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('spoken_languages')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <flux:heading size="sm">Payment Methods</flux:heading>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">

                            @foreach ($availablePaymentMethods as $method)
                                <label class="inline-flex items-center">
                                    <input type="checkbox" wire:model="payment_methods" value="{{ $method }}"
                                        class="rounded border-zinc-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-primary-600 dark:focus:ring-opacity-25">
                                    <span
                                        class="ml-2 text-sm text-zinc-700 dark:text-zinc-300">{{ ucfirst(str_replace('_', ' ', $method)) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('payment_methods')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Tags -->
                    <div class="md:col-span-2" x-data="{
                        newTag: '',
                        addTag() {
                            if (this.newTag.trim()) {
                                // Check if it contains commas (multiple tags)
                                if (this.newTag.includes(',')) {
                                    $wire.addTagsFromString(this.newTag);
                                } else {
                                    $wire.addTag(this.newTag.trim());
                                }
                                this.newTag = '';
                            }
                        },
                        handleKeydown(event) {
                            if (event.key === 'Enter') {
                                event.preventDefault();
                                this.addTag();
                            }
                        }
                    }">
                        <flux:label for="tags">Tags</flux:label>

                        <!-- Tag Input -->
                        <div class="relative">
                            <flux:input
                                id="tags"
                                x-model="newTag"
                                @keydown="handleKeydown($event)"
                                type="text"
                                placeholder="Type a tag and press Enter (or use commas for multiple tags)"
                                class="pr-20"
                            />
                            <button
                                type="button"
                                @click="addTag()"
                                class="absolute right-2 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-primary-600 text-white text-sm rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                            >
                                Add
                            </button>
                        </div>

                        <p class="text-sm text-zinc-500 mt-1">Type a tag and press Enter, or paste comma-separated tags</p>

                        <!-- Display Tags -->
                        @if(!empty($tags))
                            <div class="flex flex-wrap gap-2 mt-3">
                                @foreach($tags as $index => $tag)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                                        {{ $tag }}
                                        <button
                                            type="button"
                                            wire:click="removeTag({{ $index }})"
                                            class="ml-2 inline-flex items-center justify-center w-4 h-4 text-primary-600 hover:text-primary-800 dark:text-primary-300 dark:hover:text-primary-100"
                                        >
                                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </span>
                                @endforeach
                            </div>
                        @endif

                        @error('tags')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                        @error('tags.*')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3">
                <flux:button href="{{ route('users-management') }}" variant="outline">Cancel</flux:button>
                <flux:button type="submit" variant="primary">{{ $userId ? 'Update User' : 'Create User' }}
                </flux:button>
            </div>
        </form>
    </div>
</div>

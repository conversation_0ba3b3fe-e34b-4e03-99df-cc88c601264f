<div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-center justify-between">
        <flux:heading>User Subscription Management</flux:heading>
    </div>

    <div class="flex flex-wrap gap-4 items-center justify-between">
        <div class="flex flex-wrap gap-4 items-center">
            <!-- Search -->
            <flux:input wire:model.live.debounce.300ms="search" type="search" placeholder="Search users..."
                icon="magnifying-glass" class="w-full md:w-64" />

            <!-- Status Filter -->
            <flux:select wire:model.live="statusFilter" class="w-full md:w-40">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="trialing">Trialing</option>
                <option value="canceled">Canceled</option>
                <option value="expired">Expired</option>
                <option value="past_due">Past Due</option>
                <option value="unpaid">Unpaid</option>
            </flux:select>

            <!-- User Type Filter -->
            <flux:select wire:model.live="userTypeFilter" class="w-full md:w-40">
                <option value="">All User Types</option>
                <option value="musician">Member</option>
                <option value="client">Client</option>
            </flux:select>

            <!-- Date Range Filter -->
            <flux:select wire:model.live="dateRangeFilter" class="w-full md:w-40">
                <option value="">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="year">This Year</option>
            </flux:select>
        </div>
    </div>

    <!-- Subscriptions Table -->
    <div class="overflow-x-auto rounded-lg border border-slate-200 dark:border-slate-700">
        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead class="bg-slate-50 dark:bg-slate-800">
                <tr>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">User</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Plan</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Status</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Start Date
                    </th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">End Date
                    </th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Next
                        Billing</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Amount</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Actions
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-slate-200 bg-white dark:divide-slate-700 dark:bg-slate-900">
                @forelse ($subscriptions as $subscription)
                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                            <div class="flex items-center gap-2">
                                <div class="flex items-center">
                                    <div class="relative h-10 w-10">
                                        @if ($subscription->user->image)
                                            <img src="{{ $subscription->user->image_url }}"
                                                class="h-10 w-10 rounded-full object-cover"
                                                alt="{{ $subscription->user->name }}" />
                                        @endif
                                    </div>
                                    <div class="ml-2">
                                        <div class="font-medium">{{ $subscription->user->name }}</div>
                                        <div class="text-xs text-slate-500">{{ $subscription->user->email }}</div>
                                        <div class="text-xs text-slate-500">{{ ucfirst($subscription->user->role) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                            <div class="font-medium">{{ $subscription->plan->name }}</div>
                            <div class="text-xs text-slate-500">
                                ${{ number_format($subscription->plan->price, 2) }} /
                                {{ ucfirst(str_replace('_', ' ', $subscription->plan->billing_cycle)) }}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-col gap-1">
                                @php
                                    $statusClasses = [
                                        'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'trialing' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                        'canceled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                        'expired' => 'bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300',
                                        'past_due' => 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300',
                                        'unpaid' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                    ];
                                    $class =
                                        $statusClasses[$subscription->status] ??
                                        'bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300';
                                @endphp
                                <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $class }} text-center justify-center">
                                    {{ ucfirst($subscription->status) }}
                                </span>

                                @if ($subscription->cancel_at_period_end)
                                    <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300 text-center justify-center">
                                        Cancels at period end
                                    </span>
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                            {{ $subscription->created_at->format('M d, Y') }}
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                            {{ $subscription->ends_at ? $subscription->ends_at->format('M d, Y') : 'N/A' }}
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                            {{ $subscription->next_billing_date ? $subscription->next_billing_date->format('M d, Y') : 'N/A' }}
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">
                            @if ($subscription->transactions->isNotEmpty())
                                ${{ number_format($subscription->transactions->first()->amount, 2) }}
                            @else
                                N/A
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <!-- View Button -->
                                <button wire:click="viewSubscription({{ $subscription->id }})"
                                    title="View Subscription Details"
                                    class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-slate-900 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-100 dark:hover:bg-slate-700">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>

                                <!-- Edit Button -->
                                <button wire:click="openEditModal({{ $subscription->id }})" title="Edit Subscription"
                                    class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-slate-900 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-100 dark:hover:bg-slate-700">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                </button>

                                <!-- Refund Button (if has transactions) -->
                                @if ($subscription->transactions->isNotEmpty())
                                    <button
                                        wire:click="openRefundModal({{ $subscription->transactions->first()->id }})"
                                        title="Process Refund"
                                        class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-amber-500 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:hover:bg-slate-700">
                                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                                        </svg>
                                    </button>
                                @endif

                                <!-- Cancel Button (if active or trialing) -->
                                @if (in_array($subscription->status, ['active', 'trialing']))
                                    <button wire:click="confirmCancel({{ $subscription->id }})"
                                        title="Cancel Subscription"
                                        class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-red-500 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:hover:bg-slate-700">
                                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="8" class="px-6 py-4 text-center text-sm text-slate-500 dark:text-slate-400">No
                            subscriptions found</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div>
        {{ $subscriptions->links() }}
    </div>

    <!-- View Modal -->
    <flux:modal name="view-subscription" wire:model="isViewModalOpen" class="max-w-3xl w-full">
        <flux:heading size="lg">Subscription Details</flux:heading>

        @if ($selectedSubscription)
            <div class="mt-4 space-y-6">
                <!-- User Information -->
                <div>
                    <flux:heading size="sm">User Information</flux:heading>
                    <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <div class="text-sm font-medium text-slate-500">User</div>
                            <div class="flex items-center gap-2">
                                @if ($selectedSubscription->user->image_url)
                                    <img src="{{ $selectedSubscription->user->image_url }}"
                                        alt="{{ $selectedSubscription->user->name }}"
                                        class="h-10 w-10 rounded-full object-cover">
                                @endif
                                {{ $selectedSubscription->user->name }}
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Email</div>
                            <div>{{ $selectedSubscription->user->email }}</div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">User Type</div>
                            <div>{{ ucfirst($selectedSubscription->user->role) }}</div>
                        </div>
                    </div>
                </div>

                <!-- Plan Information -->
                <div>
                    <flux:heading size="sm">Plan Information</flux:heading>
                    <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <div class="text-sm font-medium text-slate-500">Plan Name</div>
                            <div>{{ $selectedSubscription->plan->name }}</div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Price</div>
                            <div>${{ number_format($selectedSubscription->plan->price, 2) }}</div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Billing Cycle</div>
                            <div>{{ ucfirst(str_replace('_', ' ', $selectedSubscription->plan->billing_cycle)) }}</div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Trial Days</div>
                            <div>{{ $selectedSubscription->plan->trial_days }}</div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Information -->
                <div>
                    <flux:heading size="sm">Subscription Information</flux:heading>
                    <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <div class="text-sm font-medium text-slate-500">Status</div>
                            <div>
                                @php
                                    $statusClasses = [
                                        'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'trialing' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                        'canceled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                        'expired' =>
                                            'bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300',
                                        'past_due' =>
                                            'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300',
                                        'unpaid' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                    ];
                                    $class =
                                        $statusClasses[$selectedSubscription->status] ??
                                        'bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300';
                                @endphp
                                <span
                                    class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $class }}">
                                    {{ ucfirst($selectedSubscription->status) }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Subscription ID</div>
                            <div>{{ $selectedSubscription->id }}</div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Stripe Subscription ID</div>
                            <div>{{ $selectedSubscription->stripe_subscription_id ?? 'N/A' }}</div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Created At</div>
                            <div>{{ $selectedSubscription->created_at->format('M d, Y H:i:s') }}</div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Trial Ends At</div>
                            <div>
                                {{ $selectedSubscription->trial_ends_at ? $selectedSubscription->trial_ends_at->format('M d, Y') : 'N/A' }}
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Ends At</div>
                            <div>
                                {{ $selectedSubscription->ends_at ? $selectedSubscription->ends_at->format('M d, Y') : 'N/A' }}
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Next Billing Date</div>
                            <div>
                                {{ $selectedSubscription->next_billing_date ? $selectedSubscription->next_billing_date->format('M d, Y') : 'N/A' }}
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-slate-500">Cancel At Period End</div>
                            <div>
                                @if ($selectedSubscription->cancel_at_period_end)
                                    <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
                                        Yes - Cancels at period end
                                    </span>
                                @else
                                    No
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Transaction History -->
                <div>
                    <flux:heading size="sm">Transaction History</flux:heading>
                    @if ($selectedSubscription->transactions->isNotEmpty())
                        <div class="mt-2 overflow-x-auto rounded-lg border border-slate-200 dark:border-slate-700">
                            <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                                <thead class="bg-slate-50 dark:bg-slate-800">
                                    <tr>
                                        <th scope="col"
                                            class="px-4 py-2 text-left text-xs font-semibold text-slate-900 dark:text-slate-100">
                                            Date</th>
                                        <th scope="col"
                                            class="px-4 py-2 text-left text-xs font-semibold text-slate-900 dark:text-slate-100">
                                            Amount</th>
                                        <th scope="col"
                                            class="px-4 py-2 text-left text-xs font-semibold text-slate-900 dark:text-slate-100">
                                            Status</th>
                                        <th scope="col"
                                            class="px-4 py-2 text-left text-xs font-semibold text-slate-900 dark:text-slate-100">
                                            Payment Method</th>
                                        <th scope="col"
                                            class="px-4 py-2 text-left text-xs font-semibold text-slate-900 dark:text-slate-100">
                                            Actions</th>
                                    </tr>
                                </thead>
                                <tbody
                                    class="divide-y divide-slate-200 bg-white dark:divide-slate-700 dark:bg-slate-900">
                                    @foreach ($selectedSubscription->transactions as $transaction)
                                        <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                                            <td class="px-4 py-2 text-xs text-slate-900 dark:text-slate-100">
                                                {{ $transaction->created_at->format('M d, Y H:i:s') }}
                                            </td>
                                            <td class="px-4 py-2 text-xs text-slate-900 dark:text-slate-100">
                                                ${{ number_format($transaction->amount, 2) }}
                                            </td>
                                            <td class="px-4 py-2 text-xs">
                                                <span
                                                    class="inline-flex rounded-full px-2 py-0.5 text-xs font-semibold {{ $transaction->status === 'completed'
                                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                                        : ($transaction->status === 'refunded'
                                                            ? 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
                                                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300') }}">
                                                    {{ ucfirst($transaction->status) }}
                                                </span>
                                            </td>
                                            <td class="px-4 py-2 text-xs text-slate-900 dark:text-slate-100">
                                                {{ $transaction->payment_method ?? 'N/A' }}
                                            </td>
                                            <td class="px-4 py-2 text-xs">
                                                @if ($transaction->status === 'completed')
                                                    <button wire:click="openRefundModal({{ $transaction->id }})"
                                                        class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-1.5 py-0.5 text-xs font-medium text-amber-500 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:hover:bg-slate-700">
                                                        Refund
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="mt-2 text-sm text-slate-500">No transactions found</div>
                    @endif
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <flux:button wire:click="$set('isViewModalOpen', false)">Close</flux:button>
            </div>
        @endif
    </flux:modal>

    <!-- Edit Modal -->
    <flux:modal name="edit-subscription" wire:model="isEditModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">Edit Subscription</flux:heading>

        <div class="mt-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- User Info (Read-only) -->
                <div>
                    <flux:label for="userName">User</flux:label>
                    <flux:input id="userName" wire:model="userName" disabled />
                </div>
                <div>
                    <flux:label for="userEmail">Email</flux:label>
                    <flux:input id="userEmail" wire:model="userEmail" disabled />
                </div>

                <!-- Plan Info (Read-only) -->
                <div>
                    <flux:label for="planName">Plan</flux:label>
                    <flux:input id="planName" wire:model="planName" disabled />
                </div>

                <!-- Status -->
                <div>
                    <flux:label for="status">Status</flux:label>
                    <flux:select id="status" wire:model="status">
                        <option value="active">Active</option>
                        <option value="trialing">Trialing</option>
                        <option value="canceled">Canceled</option>
                        <option value="expired">Expired</option>
                        <option value="past_due">Past Due</option>
                        <option value="unpaid">Unpaid</option>
                    </flux:select>
                    @error('status')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Trial Ends At -->
                <div>
                    <flux:label for="trialEndsAt">Trial Ends At</flux:label>
                    <flux:input id="trialEndsAt" type="date" wire:model="trialEndsAt" />
                    @error('trialEndsAt')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Ends At -->
                <div>
                    <flux:label for="endsAt">Ends At</flux:label>
                    <flux:input id="endsAt" type="date" wire:model="endsAt" />
                    @error('endsAt')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Next Billing Date -->
                <div>
                    <flux:label for="nextBillingDate">Next Billing Date</flux:label>
                    <flux:input id="nextBillingDate" type="date" wire:model="nextBillingDate" />
                    @error('nextBillingDate')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Cancel At Period End -->
                <div class="flex items-center space-x-2">
                    <flux:label for="cancelAtPeriodEnd">Cancel At Period End</flux:label>
                    <flux:switch id="cancelAtPeriodEnd" wire:model="cancelAtPeriodEnd" />
                </div>
            </div>

            <div class="mt-2 text-sm text-amber-500">
                <p>Note: Changing the status or dates here will only update the local database. For Stripe
                    subscriptions, only the "Cancel At Period End" option will be synced with Stripe.</p>
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
            <flux:button wire:click="$set('isEditModalOpen', false)">Cancel</flux:button>
            <flux:button variant="primary" wire:click="updateSubscription">Update Subscription</flux:button>
        </div>
    </flux:modal>

    <!-- Delete/Cancel Confirmation Modal -->
    <flux:modal name="delete-subscription" wire:model="isDeleteModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">Confirm Cancellation</flux:heading>

        <div class="my-4">
            @if ($selectedSubscription)
                <p>Are you sure you want to cancel the subscription for
                    <strong>{{ $selectedSubscription->user->name }}</strong>?
                </p>

                @if ($selectedSubscription->stripe_subscription_id)
                    <div class="mt-4 space-y-4">
                        <div>
                            <flux:label>Cancellation Options</flux:label>
                            <div class="mt-2 space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" wire:model="cancelAtPeriodEndOption" value="1" class="mr-2">
                                    <span>Cancel at the end of the billing period (recommended)</span>
                                </label>
                                <p class="text-sm text-slate-500 ml-6">The subscription will remain active until the end of the current billing period.</p>

                                <label class="flex items-center mt-3">
                                    <input type="radio" wire:model="cancelAtPeriodEndOption" value="0" class="mr-2">
                                    <span>Cancel immediately</span>
                                </label>
                                <p class="text-sm text-slate-500 ml-6">The subscription will be canceled immediately and access to premium features will end now.</p>
                            </div>
                        </div>
                    </div>
                @else
                    <p class="mt-2 text-amber-500">This will mark the subscription as canceled and end access to
                        premium features.</p>
                @endif

                <p class="mt-4 text-red-500">This action cannot be undone.</p>
            @endif
        </div>

        <div class="flex justify-end space-x-3">
            <flux:button wire:click="$set('isDeleteModalOpen', false)">Cancel</flux:button>
            <flux:button variant="danger" wire:click="cancelSubscription">Confirm Cancellation</flux:button>
        </div>
    </flux:modal>

    <!-- Refund Modal -->
    <flux:modal name="refund-modal" wire:model="isRefundModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">Process Refund</flux:heading>

        <div class="my-4">
            @if ($selectedTransaction)
                <p>You are about to process a refund for the transaction made by
                    <strong>{{ $selectedTransaction->user->name }}</strong>.
                </p>

                <div class="mt-4 space-y-4">
                    <!-- Full or Partial Refund -->
                    <div class="flex items-center space-x-2">
                        <flux:label for="fullRefund">Full Refund</flux:label>
                        <flux:switch id="fullRefund" wire:model.live="fullRefund" />
                    </div>

                    <!-- Refund Amount (for partial refunds) -->
                    @if (!$fullRefund)
                        <div>
                            <flux:label for="refundAmount">Refund Amount</flux:label>
                            <flux:input id="refundAmount" type="number" step="0.01" wire:model="refundAmount"
                                placeholder="0.00" />
                            <div class="text-xs text-slate-500 mt-1">Original amount:
                                ${{ number_format($selectedTransaction->amount, 2) }}</div>
                            @error('refundAmount')
                                <span class="text-red-500 text-sm">{{ $message }}</span>
                            @enderror
                        </div>
                    @endif

                    <!-- Refund Reason -->
                    <div>
                        <flux:label for="refundReason">Reason for Refund</flux:label>
                        <flux:textarea id="refundReason" wire:model="refundReason" rows="3"
                            placeholder="Provide a reason for this refund"></flux:textarea>
                        @error('refundReason')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <p class="mt-4 text-amber-500">This will process a refund through Stripe and update the transaction
                    status.</p>
                <p class="mt-2 text-red-500">This action cannot be undone.</p>
            @endif
        </div>

        <div class="flex justify-end space-x-3">
            <flux:button wire:click="$set('isRefundModalOpen', false)">Cancel</flux:button>
            <flux:button variant="danger" wire:click="processRefund">Process Refund</flux:button>
        </div>
    </flux:modal>

    <!-- Flash Message -->
    <div x-data="{
        show: false,
        message: '',
        type: '',
        showToast(message, type = 'success') {
            this.show = false;
            this.message = message;
            this.type = type;
            this.show = true;
            setTimeout(() => this.show = false, 3000);
        }
    }" @notify.window="showToast($event.detail.message, $event.detail.type)" x-show="show"
        x-transition.opacity.duration.300ms class="fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg"
        :class="type === 'error' ? 'bg-red-400 text-white' : 'bg-green-500 text-white'">
        <span x-text="message"></span>
    </div>

    @script
        <script>
            if (@js(session()->has('error'))) {
                $dispatch('notify', {
                    message: @js(session('error')),
                    type: 'error'
                });
            } else if (@js(session()->has('message'))) {
                $dispatch('notify', {
                    message: @js(session('message')),
                    type: 'success'
                });
            }
        </script>
    @endscript
</div>

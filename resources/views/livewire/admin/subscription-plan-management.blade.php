<div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-center justify-between">
        <flux:heading>Subscription Plans Management</flux:heading>

        <div class="flex items-center gap-4">
            <flux:button variant="primary" wire:click="openCreateModal" icon="plus">
                Add New Plan
            </flux:button>
        </div>
    </div>

    <div class="flex flex-wrap gap-4 items-center justify-between">
        <div class="flex flex-wrap gap-4 items-center">
            <!-- Search -->
            <flux:input wire:model.live.debounce.300ms="search" type="search" placeholder="Search plans..."
                icon="magnifying-glass" class="w-full md:w-64" />

            <!-- User Type Filter -->
            <flux:select wire:model.live="userTypeFilter" class="w-full md:w-40">
                <option value="">All User Types</option>
                <option value="musician">Member</option>
                <option value="client">Client</option>
            </flux:select>

            <!-- Billing Cycle Filter -->
            <flux:select wire:model.live="billingCycleFilter" class="w-full md:w-40">
                <option value="">All Billing Cycles</option>
                <option value="one_time">One Time</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
            </flux:select>
        </div>
    </div>

    <!-- Plans Table -->
    <div class="overflow-x-auto rounded-lg border border-slate-200 dark:border-slate-700">
        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead class="bg-slate-50 dark:bg-slate-800">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">User Type</th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Price</th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Billing Cycle</th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Trial Days</th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Status</th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Default</th>
                    <th scope="col" class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-slate-200 bg-white dark:divide-slate-700 dark:bg-slate-900">
                @forelse ($plans as $plan)
                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">{{ $plan->name }}</td>
                        <td class="px-6 py-4">
                            <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $plan->user_type === 'musician' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' }}">
                                {{ $plan->user_type === 'musician' ? 'Member' : 'Client' }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">${{ number_format($plan->price, 2) }}</td>
                        <td class="px-6 py-4">
                            <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ 
                                $plan->billing_cycle === 'one_time' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300' : 
                                ($plan->billing_cycle === 'monthly' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300') 
                            }}">
                                {{ ucfirst(str_replace('_', ' ', $plan->billing_cycle)) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-slate-900 dark:text-slate-100">{{ $plan->trial_days }}</td>
                        <td class="px-6 py-4">
                            <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $plan->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                {{ $plan->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $plan->is_default ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300' }}">
                                {{ $plan->is_default ? 'Default' : 'No' }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <button wire:click="openEditModal({{ $plan->id }})" class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-slate-900 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-100 dark:hover:bg-slate-700">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                </button>
                                <button wire:click="confirmDelete({{ $plan->id }})" class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-red-500 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:hover:bg-slate-700">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="8" class="px-6 py-4 text-center text-sm text-slate-500 dark:text-slate-400">No subscription plans found</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div>
        {{ $plans->links() }}
    </div>

    <!-- Create/Edit Modal -->
    <flux:modal name="plan-modal" wire:model="isModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">
            {{ $modalMode === 'create' ? 'Create New Subscription Plan' : 'Edit Subscription Plan' }}
        </flux:heading>

        <div class="mt-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Name -->
                <div class="md:col-span-2">
                    <flux:label for="name">Name</flux:label>
                    <flux:input id="name" wire:model="name" placeholder="Plan name" />
                    @error('name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Slug -->
                {{-- <div>
                    <flux:label for="slug">Slug (optional)</flux:label>
                    <flux:input id="slug" wire:model="slug" placeholder="plan-slug" />
                    <div class="text-xs text-slate-500 mt-1">Leave empty to auto-generate</div>
                </div> --}}

                <!-- User Type -->
                <div>
                    <flux:label for="user_type">User Type</flux:label>
                    <flux:select id="user_type" wire:model="user_type">
                        <option value="musician">Member</option>
                        <option value="client">Client</option>
                    </flux:select>
                    @error('user_type') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Price -->
                <div>
                    <flux:label for="price">Price</flux:label>
                    <flux:input id="price" type="number" step="0.01" wire:model="price" placeholder="0.00" />
                    @error('price') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Billing Cycle -->
                <div>
                    <flux:label for="billing_cycle">Billing Cycle</flux:label>
                    <flux:select id="billing_cycle" wire:model="billing_cycle">
                        <option value="one_time">One Time</option>
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                    </flux:select>
                    @error('billing_cycle') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Trial Days -->
                <div>
                    <flux:label for="trial_days">Trial Days</flux:label>
                    <flux:input id="trial_days" type="number" wire:model="trial_days" placeholder="0" />
                    @error('trial_days') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <flux:label for="description">Description</flux:label>
                <flux:textarea id="description" wire:model="description" rows="3" placeholder="Plan description"></flux:textarea>
                @error('description') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
            </div>

            <!-- Features -->
            <div>
                <flux:label>Features</flux:label>
                <div class="flex items-center space-x-2 mb-2">
                    <flux:input wire:model="newFeature" placeholder="Add a feature" wire:keydown.enter.prevent="addFeature" />
                    <flux:button type="button" wire:click="addFeature">
                        <flux:icon name="plus" class="h-4 w-4" />
                    </flux:button>
                </div>
                
                <div class="space-y-2 mt-3">
                    @if (count($features) > 0)
                        <ul class="space-y-2">
                            @foreach ($features as $index => $feature)
                                <li class="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded-md">
                                    <span>{{ $feature }}</span>
                                    <flux:button size="xs" variant="ghost" class="text-red-500" wire:click="removeFeature({{ $index }})">
                                        <flux:icon name="x-mark" class="h-4 w-4" />
                                    </flux:button>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <div class="text-slate-500 text-sm">No features added yet</div>
                    @endif
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Active Status -->
                <div class="flex items-center space-x-2">
                    <flux:label for="is_active">Active</flux:label>
                    <flux:switch id="is_active" wire:model="is_active" />
                </div>

                <!-- Default Plan -->
                <div class="flex items-center space-x-2">
                    <flux:label for="is_default">Default Plan</flux:label>
                    <flux:switch id="is_default" wire:model="is_default" />
                </div>
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
            <flux:button wire:click="$set('isModalOpen', false)">Cancel</flux:button>
            <flux:button variant="primary" wire:click="savePlan">
                {{ $modalMode === 'create' ? 'Create Plan' : 'Update Plan' }}
            </flux:button>
        </div>
    </flux:modal>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="delete-plan" wire:model="isDeleteModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">Confirm Deletion</flux:heading>

        <div class="my-4">
            @if ($selectedPlan)
                <p>Are you sure you want to delete the plan <strong>{{ $selectedPlan->name }}</strong>?</p>
                <p class="mt-2 text-red-500">This action cannot be undone.</p>
            @endif
        </div>

        <div class="flex justify-end space-x-3">
            <flux:button wire:click="$set('isDeleteModalOpen', false)">Cancel</flux:button>
            <flux:button variant="danger" wire:click="deletePlan">Delete</flux:button>
        </div>
    </flux:modal>
</div>

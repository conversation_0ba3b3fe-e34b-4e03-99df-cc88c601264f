<div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-center justify-between">
        <flux:heading>Users Management</flux:heading>

        <div class="flex items-center gap-4">
            <div class="flex items-center space-x-4 max-w-2xl w-full">
                <!-- Search -->
                <flux:input wire:model.live.debounce.300ms="search" type="search" placeholder="Search users..."
                    icon="magnifying-glass" />

                <!-- Role Filter -->
                <flux:select wire:model.live="roleFilter">
                    <option value="">All Roles</option>
                    <option value="musician">Member</option>
                    <option value="client">Client</option>
                </flux:select>
            </div>

            <!-- Add User Button -->
            <flux:button href="{{ route('users.create') }}" variant="primary" icon="plus">
                Add User
            </flux:button>
        </div>
    </div>
    <!-- Users Table -->
    <div class="overflow-x-auto rounded-lg border border-zinc-200 dark:border-zinc-700">
        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
            <thead class="bg-zinc-50 dark:bg-zinc-800">
                <tr>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">User</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">Email</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">Role</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-zinc-900 dark:text-zinc-100">Status</th>
                    <th scope="col"
                        class="px-6 py-3 text-right text-sm font-semibold text-zinc-900 dark:text-zinc-100">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                @forelse ($users as $user)
                    <tr wire:key="{{ $user->id }}" class="hover:bg-zinc-50 dark:hover:bg-zinc-800/50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center gap-3">
                                <div class="relative h-10 w-10">
                                    @if ($user->image)
                                        <img src="{{ $user->image_url }}" class="h-10 w-10 rounded-full object-cover"
                                            alt="{{ $user->name }}" />
                                    @else
                                        <div
                                            class="flex h-10 w-10 items-center justify-center rounded-full bg-zinc-100 dark:bg-zinc-700">
                                            <span
                                                class="text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ $user->initials() }}</span>
                                        </div>
                                    @endif
                                </div>
                                <div class="flex flex-col">
                                    <span
                                        class="font-medium text-zinc-900 dark:text-zinc-100">{{ $user->name }}</span>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-zinc-600 dark:text-zinc-300">
                            {{ $user->email }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span
                                class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $user->role === 'musician' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : 'bg-zinc-100 text-zinc-800 dark:bg-zinc-700 dark:text-zinc-300' }}">
                                @if ($user->role === 'musician')
                                    Member
                                @else
                                    Client
                                @endif
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span
                                class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $user->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex justify-end space-x-2">
                                <button wire:click="viewUser({{ $user->id }})"
                                    class="rounded-lg p-1 text-zinc-500 hover:bg-zinc-100 hover:text-zinc-700 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-200"
                                    title="View Details">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>
                                @if ($user->role === 'musician')
                                    <button wire:click="viewRatings({{ $user->id }})"
                                        class="rounded-lg p-1 text-zinc-500 hover:bg-zinc-100 hover:text-yellow-500 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-yellow-400"
                                        title="View Ratings">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                    </button>
                                @endif
                                <a href="{{ route('users.edit', $user->id) }}"
                                    class="rounded-lg p-1 text-zinc-500 hover:bg-zinc-100 hover:text-zinc-700 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-200"
                                    title="Edit User">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                </a>
                                <button wire:click="confirmDelete({{ $user->id }})"
                                    class="rounded-lg p-1 text-zinc-500 hover:bg-zinc-100 hover:text-red-600 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-red-500"
                                    title="Delete User">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                            No users found.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <div>
        {{ $users->links() }}
    </div>

    <!-- Edit Modal -->
    <flux:modal name="edit-user" wire:model="isEditModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">
            Edit User
        </flux:heading>

        @if ($selectedUser)
            <div class="space-y-4 mt-3">
                <div class="grid gap-4">
                    @if ($selectedUser->role === 'musician')
                        <div class="flex items-center justify-between">
                            <span class="font-medium">Active Status</span>
                            <flux:switch wire:model="is_active" />
                        </div>
                    @endif
                </div>
            </div>
        @endif

        <flux:button class="mt-5" wire:click="$set('isEditModalOpen', false)">Close</flux:button>
        <flux:button variant="primary" class="mt-5 ms-3" wire:click="updateUser">Save</flux:button>
    </flux:modal>

    <!-- View Modal -->
    <flux:modal name="view-user" wire:model="isViewModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">
            User Details

        </flux:heading>

        @if ($selectedUser)
            <div class="space-y-6 mt-5">
                <!-- Basic Info -->
                <div class="space-y-4">
                    <flux:heading size="sm">
                        Basic Information
                    </flux:heading>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="text-sm font-medium text-slate-500">Name</label>
                            <p>{{ $selectedUser->name }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-slate-500">Email</label>
                            <p>{{ $selectedUser->email }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-slate-500">Role</label>
                            <p>
                                @if ($selectedUser->role === 'musician')
                                    Member
                                @else
                                    Client
                                @endif
                            </p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-slate-500">Active Status</label>
                            <br>
                            <flux:badge :variant="$selectedUser->is_active ? 'success' : 'danger'">
                                {{ $selectedUser->is_active ? 'Active' : 'Inactive' }}
                            </flux:badge>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-slate-500">Verified Status</label>
                            <br>
                            <flux:badge :variant="$selectedUser->is_verified ? 'success' : 'danger'">
                                {{ $selectedUser->is_verified ? 'Verified' : 'Not Verified' }}
                            </flux:badge>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-slate-500">Availability</label>
                            <br>
                            <flux:badge :variant="$selectedUser->availability ? 'success' : 'danger'">
                                {{ $selectedUser->availability ? 'Available' : 'Not Available' }}
                            </flux:badge>
                        </div>
                    </div>
                </div>

                @if ($selectedUser->role === 'musician' && $selectedUser->musicianProfile)
                    <!-- Member Profile -->
                    <div class="space-y-4">
                        <flux:heading size="sm">Member Profile</flux:heading>

                        @if ($selectedUser->musicianProfile->header_image)
                            <div class="col-span-2">
                                <label class="text-sm font-medium text-slate-500">Header Image</label>
                                <div class="mt-1">
                                    <img src="{{ $selectedUser->musicianProfile->header_image_url }}"
                                        alt="Header Image" class="h-32 w-full object-cover rounded-lg">
                                </div>
                            </div>
                        @endif

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="text-sm font-medium text-slate-500">Phone Number</label>
                                <p>{{ $selectedUser->musicianProfile->phone_number ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Website</label>
                                <p>{{ $selectedUser->musicianProfile->website ?? 'N/A' }}</p>
                            </div>
                            <div class="col-span-2">
                                <label class="text-sm font-medium text-slate-500">Description</label>
                                <p>{{ $selectedUser->musicianProfile->description ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Location</label>
                                <p>{{ $selectedUser->musicianProfile->location ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Rating</label>
                                <p>
                                    @if ($selectedUser->musicianProfile->average_rating)
                                        {{ number_format($selectedUser->musicianProfile->average_rating, 1) }} ⭐
                                        ({{ $selectedUser->musicianProfile->ratings_count ?? 0 }} ratings)
                                    @else
                                        No ratings yet
                                    @endif
                                </p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Rate per hour</label>
                                <p>${{ number_format($selectedUser->musicianProfile->rate_per_hour ?? 0, 2) }}</p>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Rate per event</label>
                                <p>${{ number_format($selectedUser->musicianProfile->rate_per_event ?? 0, 2) }}</p>
                            </div>

                            <!-- Social Links -->
                            <div class="col-span-2">
                                <label class="text-sm font-medium text-slate-500">Social Links</label>
                                <div class="flex flex-col gap-2 mt-1">
                                    @if (!empty($selectedUser->musicianProfile->social_links))
                                        @foreach ($selectedUser->musicianProfile->social_links as $socialLink)
                                            @if (!empty($socialLink['value']) && !empty($socialLink['key']))
                                                <a href="{{ $socialLink['value'] }}" target="_blank"
                                                    class="inline-flex items-center text-blue-600 hover:underline">
                                                    {{ ucfirst($socialLink['key']) }}
                                                </a>
                                            @endif
                                        @endforeach
                                    @endif
                                </div>
                            </div>

                            <div>
                                <label class="text-sm font-medium text-slate-500">Roles</label>
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($selectedUser->musicianProfile->roles ?? [] as $role)
                                        <flux:badge size="sm">{{ ucfirst($role) }}</flux:badge>
                                    @endforeach
                                </div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Services</label>
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($selectedUser->musicianProfile->offered_services ?? [] as $service)
                                        <flux:badge size="sm">{{ ucfirst($service) }}</flux:badge>
                                    @endforeach
                                </div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Instruments</label>
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($selectedUser->musicianProfile->instruments ?? [] as $instrument)
                                        <flux:badge size="sm">{{ ucfirst($instrument) }}</flux:badge>
                                    @endforeach
                                </div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Music Types</label>
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($selectedUser->musicianProfile->music_types ?? [] as $type)
                                        <flux:badge size="sm">{{ ucfirst($type) }}</flux:badge>
                                    @endforeach
                                </div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Spoken Languages</label>
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($selectedUser->musicianProfile->spoken_languages ?? [] as $language)
                                        <flux:badge size="sm">{{ ucfirst($language) }}</flux:badge>
                                    @endforeach
                                </div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Payment Methods</label>
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($selectedUser->musicianProfile->payment_methods ?? [] as $method)
                                        <flux:badge size="sm">{{ ucfirst(str_replace('_', ' ', $method)) }}
                                        </flux:badge>
                                    @endforeach
                                </div>
                            </div>
                            <div>
                                <label class="text-sm font-medium text-slate-500">Tags</label>
                                <div class="flex flex-wrap gap-1">
                                    @foreach ($selectedUser->musicianProfile->tags ?? [] as $tag)
                                        <flux:badge size="sm">{{ ucfirst($tag) }}</flux:badge>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        <flux:button class="mt-5" wire:click="$set('isViewModalOpen', false)">Close</flux:button>
    </flux:modal>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="delete-user" wire:model="isDeleteModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">Confirm Deletion</flux:heading>

        <div class="my-3">
            <p>Are you sure you want to delete this user? This action cannot be undone.</p>
        </div>

        <div class="flex space-x-2">
            <flux:button wire:click="$set('isDeleteModalOpen', false)">Cancel</flux:button>
            <flux:button variant="danger" wire:click="deleteUser">Delete</flux:button>
        </div>
    </flux:modal>

    <!-- Ratings Modal -->
    <flux:modal name="ratings-modal" wire:model="isRatingsModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">
            Musician Ratings
        </flux:heading>

        @if ($selectedUser && $selectedUser->musicianProfile)
            <div class="mt-4">
                <div class="flex items-center mb-4">
                    <div
                        class="flex-shrink-0 h-10 w-10 rounded-full overflow-hidden bg-slate-200 dark:bg-slate-700 mr-3">
                        @if ($selectedUser->image_url)
                            <img src="{{ $selectedUser->image_url }}" alt="{{ $selectedUser->name }}"
                                class="h-full w-full object-cover">
                        @else
                            <div class="h-full w-full flex items-center justify-center text-sm font-medium">
                                {{ substr($selectedUser->name, 0, 1) }}
                            </div>
                        @endif
                    </div>
                    <div>
                        <div class="font-medium">{{ $selectedUser->name }}</div>
                        <div class="text-sm text-slate-500">
                            @if ($selectedUser->musicianProfile->average_rating)
                                {{ number_format($selectedUser->musicianProfile->average_rating, 1) }} ⭐
                                ({{ $selectedUser->musicianProfile->ratings_count ?? 0 }} ratings)
                            @else
                                No ratings yet
                            @endif
                        </div>
                    </div>
                </div>

                <div class="space-y-4 mt-4 max-h-96 overflow-y-auto">
                    @forelse($ratings as $rating)
                        <div wire:key="rating-{{ $rating->id }}" class="border border-slate-200 dark:border-slate-700 rounded-lg p-4 mt-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div
                                        class="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden bg-slate-200 dark:bg-slate-700 mr-3">
                                        @if ($rating->user && $rating->user->image_url)
                                            <img src="{{ $rating->user->image_url }}"
                                                alt="{{ $rating->user->name }}" class="h-full w-full object-cover">
                                        @else
                                            <div
                                                class="h-full w-full flex items-center justify-center text-xs font-medium">
                                                {{ $rating->user ? substr($rating->user->name, 0, 1) : '?' }}
                                            </div>
                                        @endif
                                    </div>
                                    <div>
                                        <div class="font-medium">{{ $rating->user->name ?? 'Unknown User' }}</div>
                                        <div class="text-xs text-slate-500">{{ $rating->created_at?->diffForHumans() }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-yellow-500 font-medium">{{ $rating->rating }}</span>
                                    <svg class="h-5 w-5 text-yellow-500 ml-1" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                </div>
                            </div>
                            @if ($rating->comment)
                                <div class="mt-2 text-sm">
                                    {{ $rating->comment }}
                                </div>
                            @endif
                        </div>
                    @empty
                        <div class="text-center py-8 text-slate-500">
                            No ratings found for this musician.
                        </div>
                    @endforelse
                </div>
            </div>
        @endif

        <flux:button class="mt-5" wire:click="$set('isRatingsModalOpen', false)">Close</flux:button>
    </flux:modal>
</div>

<div class="space-y-6">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <div class="flex flex-wrap gap-4 items-center justify-between">
        <h2 class="text-2xl font-bold">Dashboard</h2>

        {{-- <div class="flex items-center gap-2">
            <select wire:model.live="timeRange"
                class="block w-40 rounded-lg border border-slate-200 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:ring-primary-500 dark:border-slate-700 dark:bg-slate-800">
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="year">Last Year</option>
            </select>
        </div> --}}
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="bg-gradient-to-br from-white to-blue-50 dark:from-slate-800 dark:to-slate-900 rounded-xl border border-blue-100 dark:border-blue-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-blue-600 dark:text-blue-400">Total Users</div>
                        <div class="text-3xl font-bold mt-1">
                            {{ number_format($this->totalUsers) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-5 grid grid-cols-2 gap-4 pt-4 border-t border-blue-100 dark:border-blue-900/30">
                    <div class="flex flex-col">
                        <span class="text-2xl font-semibold text-purple-600 dark:text-purple-400">
                            {{ number_format($this->musicianCount) }}
                        </span>
                        <span class="text-sm font-medium text-purple-500 dark:text-purple-400/70">Members</span>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-2xl font-semibold text-green-600 dark:text-green-400">
                            {{ number_format($this->clientCount) }}
                        </span>
                        <span class="text-sm font-medium text-green-500 dark:text-green-400/70">Clients</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="bg-gradient-to-br from-white to-green-50 dark:from-slate-800 dark:to-slate-900 rounded-xl border border-green-100 dark:border-green-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-green-600 dark:text-green-400">Active Subscriptions</div>
                        <div class="text-3xl font-bold mt-1">
                            {{ number_format($this->activeSubscriptions) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-5 flex items-center gap-2 pt-4 border-t border-green-100 dark:border-green-900/30">
                    <div class="flex items-center gap-1.5">
                        <div class="p-1 rounded-full bg-green-100 dark:bg-green-900/50">
                            <svg class="h-3 w-3 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </div>
                        <span class="text-2xl font-semibold text-green-600 dark:text-green-400">
                            {{ number_format($this->newSubscriptions) }}
                        </span>
                    </div>
                    <span class="text-sm font-medium text-green-500 dark:text-green-400/70">new this month</span>
                </div>
            </div>
        </div>

        <!-- Monthly Revenue -->
        <div class="bg-gradient-to-br from-white to-amber-50 dark:from-slate-800 dark:to-slate-900 rounded-xl border border-amber-100 dark:border-amber-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-amber-600 dark:text-amber-400">Monthly Revenue</div>
                        <div class="text-3xl font-bold mt-1">
                            ${{ number_format($this->monthlyRevenue, 2) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-amber-100 dark:bg-amber-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-amber-600 dark:text-amber-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-5 flex items-center gap-2 pt-4 border-t border-amber-100 dark:border-amber-900/30">
                    <span class="text-2xl font-semibold text-amber-600 dark:text-amber-400">
                        ${{ number_format($this->totalRevenue, 2) }}
                    </span>
                    <span class="text-sm font-medium text-amber-500 dark:text-amber-400/70">total revenue</span>
                </div>
            </div>
        </div>

        <!-- New Users -->
        <div class="bg-gradient-to-br from-white to-indigo-50 dark:from-slate-800 dark:to-slate-900 rounded-xl border border-indigo-100 dark:border-indigo-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-indigo-600 dark:text-indigo-400">New Users</div>
                        <div class="text-3xl font-bold mt-1">
                            {{ number_format($this->newUsers) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-5 pt-4 border-t border-indigo-100 dark:border-indigo-900/30">
                    <span class="text-sm font-medium text-indigo-500 dark:text-indigo-400/70">This month</span>
                </div>
            </div>
        </div>
    </div>


    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- User Registrations Chart -->
        <div
            class="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm">
            <div class="border-b border-slate-200 dark:border-slate-700 px-4 py-3">
                <h3 class="text-lg font-medium">User Registrations</h3>
            </div>
            <div wire:ignore class="p-4">
                <div x-data="{
                    chartData: @entangle('chartData'),
                    init() {
                        this.initChart();
                        this.$watch('chartData', () => {
                            this.updateChart();
                        });
                    },
                    chart: null,
                    initChart() {
                        const ctx = document.getElementById('registrationsChart').getContext('2d');
                        this.chart = new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: this.chartData.labels || [],
                                datasets: [{
                                    label: 'New Users',
                                    data: this.chartData.registrations || [],
                                    borderColor: '#6366f1',
                                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                                    borderWidth: 2,
                                    tension: 0.3,
                                    fill: true
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: false
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        }
                                    }
                                }
                            }
                        });
                    },
                    updateChart() {
                        if (this.chart) {
                            this.chart.data.labels = this.chartData.labels || [];
                            this.chart.data.datasets[0].data = this.chartData.registrations || [];
                            this.chart.update();
                        }
                    }
                }" class="h-80">
                    <canvas id="registrationsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Revenue Chart -->
        <div
            class="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm">
            <div class="border-b border-slate-200 dark:border-slate-700 px-4 py-3">
                <h3 class="text-lg font-medium">Revenue</h3>
            </div>
            <div wire:ignore class="p-4">
                <div x-data="{
                    chartData: @entangle('chartData'),
                    init() {
                        this.initChart();
                        this.$watch('chartData', () => {
                            this.updateChart();
                        });
                    },
                    chart: null,
                    initChart() {
                        const ctx = document.getElementById('revenueChart').getContext('2d');
                        this.chart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: this.chartData.labels || [],
                                datasets: [{
                                    label: 'Revenue ($)',
                                    data: this.chartData.revenue || [],
                                    backgroundColor: 'rgba(245, 158, 11, 0.5)',
                                    borderColor: '#f59e0b',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: false
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    },
                    updateChart() {
                        if (this.chart) {
                            this.chart.data.labels = this.chartData.labels || [];
                            this.chart.data.datasets[0].data = this.chartData.revenue || [];
                            this.chart.update();
                        }
                    }
                }" class="h-80">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Stats -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Top Musicians -->
        <div
            class="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm">
            <div class="border-b border-slate-200 dark:border-slate-700 px-4 py-3">
                <h3 class="text-lg font-medium">Top Musicians</h3>
            </div>
            <div class="divide-y divide-slate-200 dark:divide-slate-700">
                @forelse($this->topMusicians as $musician)
                    <div class="flex items-center justify-between p-4">
                        <div class="flex items-center">
                            <div
                                class="flex-shrink-0 h-10 w-10 rounded-full overflow-hidden bg-slate-200 dark:bg-slate-700 mr-3">
                                @if ($musician->image_url)
                                    <img src="{{ $musician->image_url }}" alt="{{ $musician->name }}"
                                        class="h-full w-full object-cover">
                                @else
                                    <div class="h-full w-full flex items-center justify-center text-sm font-medium">
                                        {{ substr($musician->name, 0, 1) }}
                                    </div>
                                @endif
                            </div>
                            <div>
                                <div class="font-medium">{{ $musician->name }}</div>
                                <div class="text-xs text-slate-500">{{ $musician->email }}</div>
                            </div>
                        </div>
                        <div
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">
                            {{ $musician->favorite_count }} Favorites
                        </div>
                    </div>
                @empty
                    <div class="p-4 text-center text-slate-500">
                        No data available
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Recent Transactions -->
        <div
            class="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm">
            <div class="border-b border-slate-200 dark:border-slate-700 px-4 py-3">
                <h3 class="text-lg font-medium">Recent Transactions</h3>
            </div>
            <div class="divide-y divide-slate-200 dark:divide-slate-700">
                @forelse($this->recentTransactions as $transaction)
                    <div class="flex items-center justify-between p-4">
                        <div class="flex items-center">
                            <div
                                class="flex-shrink-0 h-10 w-10 rounded-full overflow-hidden bg-slate-200 dark:bg-slate-700 mr-3">
                                @if ($transaction->user && $transaction->user->image_url)
                                    <img src="{{ $transaction->user->image_url }}"
                                        alt="{{ $transaction->user->name }}" class="h-full w-full object-cover">
                                @else
                                    <div class="h-full w-full flex items-center justify-center text-sm font-medium">
                                        {{ $transaction->user ? substr($transaction->user->name, 0, 1) : '?' }}
                                    </div>
                                @endif
                            </div>
                            <div>
                                <div class="font-medium">
                                    {{ $transaction->user ? $transaction->user->name : 'Unknown User' }}</div>
                                <div class="text-xs text-slate-500">{{ $transaction->created_at->format('M d, Y') }}
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="font-medium">${{ number_format($transaction->amount, 2) }}</div>
                            <div class="text-xs text-slate-500">{{ $transaction->payment_method }}</div>
                        </div>
                    </div>
                @empty
                    <div class="p-4 text-center text-slate-500">
                        No recent transactions
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Platform Status -->
        <div
            class="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm">
            <div class="border-b border-slate-200 dark:border-slate-700 px-4 py-3">
                <h3 class="text-lg font-medium">Platform Status</h3>
            </div>
            <div class="p-4">
                <div class="space-y-4">
                    <!-- Active Banners -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="p-2 rounded-full bg-pink-100 dark:bg-pink-900/30 mr-3">
                                <svg class="h-5 w-5 text-pink-600 dark:text-pink-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0 2 2 0 010 2.828L10.828 16 16 10.828a2 2 0 012.828 0 2 2 0 010 2.828l-6 6a2 2 0 01-2.828 0z" />
                                </svg>
                            </div>
                            <div>Active Banners</div>
                        </div>
                        <div class="font-medium">{{ $this->activeBanners }}</div>
                    </div>

                    <!-- Unread Notifications -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="p-2 rounded-full bg-orange-100 dark:bg-orange-900/30 mr-3">
                                <svg class="h-5 w-5 text-orange-600 dark:text-orange-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 17h5l-1.405-1.405a2.032 2.032 0 01-.537-1.397L14.5 10.808c-.391-.777-.99-1.307-1.668-1.61A9.06 9.06 0 006 11.7c.392.77.99 1.3 1.668 1.61a4.5 4.5 0 011.397.537L15 17z" />
                                </svg>
                            </div>
                            <div>Unread Notifications</div>
                        </div>
                        <div class="font-medium">{{ $this->unreadNotifications }}</div>
                    </div>

                    <!-- User Distribution -->
                    <div class="mt-6">
                        <div class="text-sm font-medium mb-2">User Distribution</div>
                        <div x-data="{
                            init() {
                                const ctx = document.getElementById('userDistributionChart').getContext('2d');
                                new Chart(ctx, {
                                    type: 'doughnut',
                                    data: {
                                        labels: ['Musicians', 'Clients'],
                                        datasets: [{
                                            data: [{{ $this->musicianCount }}, {{ $this->clientCount }}],
                                            backgroundColor: [
                                                'rgba(168, 85, 247, 0.7)',
                                                'rgba(34, 197, 94, 0.7)'
                                            ],
                                            borderColor: [
                                                'rgba(168, 85, 247, 1)',
                                                'rgba(34, 197, 94, 1)'
                                            ],
                                            borderWidth: 1
                                        }]
                                    },
                                    options: {
                                        responsive: true,
                                        maintainAspectRatio: false,
                                        plugins: {
                                            legend: {
                                                position: 'bottom'
                                            }
                                        },
                                        cutout: '70%'
                                    }
                                });
                            }
                        }" class="h-48">
                            <canvas id="userDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

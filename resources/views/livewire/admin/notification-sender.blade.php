<div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-center justify-between">
        <flux:heading>Send Push Notifications</flux:heading>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Notification Form -->
        <div class="lg:col-span-1 space-y-6">
            <div class="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm">
                <div class="border-b border-slate-200 dark:border-slate-700 px-6 py-4">
                    <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">Notification Details</h3>
                </div>
                <div class="p-6 space-y-4">
                    <!-- Title -->
                    <div>
                        <flux:label for="title">Title</flux:label>
                        <flux:input id="title" wire:model="title" placeholder="Notification title" />
                        @error('title') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- Body -->
                    <div>
                        <flux:label for="body">Message</flux:label>
                        <flux:textarea id="body" wire:model="body" rows="4" placeholder="Notification message"></flux:textarea>
                        @error('body') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- Target Type -->
                    <div>
                        <label class="block text-sm font-medium text-slate-900 dark:text-slate-100">Send To</label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                            <div class="flex items-center">
                                <input 
                                    type="radio" 
                                    id="target-all" 
                                    name="targetType" 
                                    value="all" 
                                    wire:model.live="targetType"
                                    class="h-4 w-4 text-primary-600 border-slate-300 focus:ring-primary-500 dark:border-slate-600 dark:bg-slate-800 dark:focus:ring-primary-600"
                                >
                                <label for="target-all" class="ml-2 block text-sm text-slate-900 dark:text-slate-100">
                                    All Users
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input 
                                    type="radio" 
                                    id="target-group" 
                                    name="targetType" 
                                    value="group" 
                                    wire:model.live="targetType"
                                    class="h-4 w-4 text-primary-600 border-slate-300 focus:ring-primary-500 dark:border-slate-600 dark:bg-slate-800 dark:focus:ring-primary-600"
                                >
                                <label for="target-group" class="ml-2 block text-sm text-slate-900 dark:text-slate-100">
                                    User Group
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input 
                                    type="radio" 
                                    id="target-specific" 
                                    name="targetType" 
                                    value="specific" 
                                    wire:model.live="targetType"
                                    class="h-4 w-4 text-primary-600 border-slate-300 focus:ring-primary-500 dark:border-slate-600 dark:bg-slate-800 dark:focus:ring-primary-600"
                                >
                                <label for="target-specific" class="ml-2 block text-sm text-slate-900 dark:text-slate-100">
                                    Specific Users
                                </label>
                            </div>
                        </div>
                        @error('targetType') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- User Group Selection (shown when targetType is 'group') -->
                    @if ($targetType === 'group')
                        <div class="mt-4">
                            <flux:label for="userGroup">Select Group</flux:label>
                            <flux:select id="userGroup" wire:model.live="userGroup">
                                <option value="all">All Users</option>
                                <option value="musician">Members</option>
                                <option value="client">Clients</option>
                            </flux:select>
                            @error('userGroup') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    @endif

                    <!-- Send Button -->
                    <div class="pt-4">
                        <flux:button variant="primary" wire:click="sendNotification" wire:loading.attr="disabled" class="w-full md:w-auto">
                            <div wire:loading wire:target="sendNotification" class="mr-2">
                                <flux:icon name="arrow-path" class="h-4 w-4 animate-spin" />
                            </div>
                            Send to {{ $userCount }} {{ Str::plural('User', $userCount) }}
                        </flux:button>
                    </div>

                    <!-- Result Message -->
                    @if ($result)
                        <div class="mt-4 p-4 rounded-md {{ $result['success'] ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400' : 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400' }}">
                            {{ $result['message'] }}
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- User Selection (shown when targetType is 'specific') -->
        <div class="lg:col-span-1">
            @if ($targetType === 'specific')
                <div class="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm">
                    <div class="border-b border-slate-200 dark:border-slate-700 px-6 py-4 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">Select Users</h3>
                        <div class="flex items-center space-x-2">
                            <flux:checkbox id="select-all" wire:model.live="selectAll" />
                            <flux:label for="select-all" class="text-sm">Select All</flux:label>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="mb-4 space-y-2">
                            <!-- Search and Filter -->
                            <flux:input wire:model.live.debounce.300ms="search" type="search" placeholder="Search users..." icon="magnifying-glass" />
                            
                            <flux:select wire:model.live="roleFilter" class="w-full">
                                <option value="">All Roles</option>
                                <option value="musician">Members</option>
                                <option value="client">Clients</option>
                            </flux:select>
                        </div>

                        <div class="border rounded-md dark:border-slate-700 overflow-hidden">
                            @if (count($users) > 0)
                                <div class="max-h-96 overflow-y-auto">
                                    <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
                                        <tbody class="bg-white dark:bg-slate-900 divide-y divide-slate-200 dark:divide-slate-700">
                                            @foreach ($users as $user)
                                                <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                                                    <td class="w-10 px-6 py-4">
                                                        <flux:checkbox id="user-{{ $user->id }}" value="{{ $user->id }}" wire:model.live="selectedUsers" />
                                                    </td>
                                                    <td class="px-6 py-4">
                                                        <label for="user-{{ $user->id }}" class="flex items-center cursor-pointer">
                                                            <div class="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden bg-slate-200 dark:bg-slate-700 mr-3">
                                                                @if ($user->image_url)
                                                                    <img src="{{ $user->image_url }}" alt="{{ $user->name }}" class="h-full w-full object-cover">
                                                                @else
                                                                    <div class="h-full w-full flex items-center justify-center text-sm font-medium">
                                                                        {{ substr($user->name, 0, 1) }}
                                                                    </div>
                                                                @endif
                                                            </div>
                                                            <div>
                                                                <div class="font-medium">{{ $user->name }}</div>
                                                                <div class="text-xs text-slate-500">{{ $user->email }}</div>
                                                            </div>
                                                        </label>
                                                    </td>
                                                    <td class="px-6 py-4 text-right">
                                                        <flux:badge :variant="$user->role === 'musician' ? 'purple' : 'green'">
                                                            @if ($user->role === 'musician')
                                                                Member
                                                                @else
                                                                Client
                                                            @endif
                                                        </flux:badge>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="p-4 text-center text-slate-500">
                                    No users found matching your search criteria.
                                </div>
                            @endif
                        </div>

                        @error('selectedUsers') 
                            <div class="mt-2 text-red-500 text-sm">{{ $message }}</div>
                        @enderror

                        <div class="mt-4 text-sm text-slate-500">
                            {{ count($selectedUsers) }} {{ Str::plural('user', count($selectedUsers)) }} selected
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<div>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/typography@0.5.16/src/index.min.js"></script>
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <flux:heading size="xl">Edit {{ $page->title }}</flux:heading>
            <flux:subheading>Update the content for {{ $page->slug }}</flux:subheading>
        </div>
        <flux:button wire:click="goBack" variant="ghost" icon="arrow-left">
            Back to Pages
        </flux:button>
    </div>

    <!-- Edit Form -->
    <form wire:submit="save" class="space-y-6">
        <!-- Title -->
        <div>
            <flux:label for="title">Title</flux:label>
            <flux:input id="title" wire:model="title" placeholder="Page title" required />
            @error('title')
                <flux:error>{{ $message }}</flux:error>
            @enderror
        </div>

        <!-- Content -->
        <div class="prose lg:prose-xl">
            <flux:label for="content">Content</flux:label>
            <div wire:ignore>
                <textarea id="content" wire:model="content" class="w-full" style="min-height: 500px;"></textarea>
            </div>
            @error('content')
                <flux:error>{{ $message }}</flux:error>
            @enderror
        </div>

        <!-- Status -->
        <div>
            <flux:checkbox wire:model="is_active" label="Active (visible to users)" />
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-slate-200 dark:border-slate-700">
            <flux:button type="button" variant="ghost" wire:click="goBack">
                Cancel
            </flux:button>
            <flux:button type="submit" variant="primary">
                Update Page
            </flux:button>
        </div>
    </form>
</div>

@push('scripts')
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>
        document.addEventListener('livewire:init', () => {
            let editor;

            // Initialize CKEditor when component loads
            initializeCKEditor();

            // Reinitialize on Livewire updates
            Livewire.hook('morph.updated', () => {
                if (!editor) {
                    initializeCKEditor();
                }
            });

            function initializeCKEditor() {
                const contentTextarea = document.getElementById('content');
                
                if (contentTextarea && !editor) {
                    ClassicEditor
                        .create(contentTextarea, {
                            toolbar: [
                                'heading', '|',
                                'bold', 'italic', 'link', '|',
                                'bulletedList', 'numberedList', '|',
                                'outdent', 'indent', '|',
                                'blockQuote', 'insertTable', '|',
                                'undo', 'redo'
                            ],
                            heading: {
                                options: [
                                    {
                                        model: 'paragraph',
                                        title: 'Paragraph',
                                        class: 'ck-heading_paragraph'
                                    },
                                    {
                                        model: 'heading1',
                                        view: 'h1',
                                        title: 'Heading 1',
                                        class: 'ck-heading_heading1'
                                    },
                                    {
                                        model: 'heading2',
                                        view: 'h2',
                                        title: 'Heading 2',
                                        class: 'ck-heading_heading2'
                                    },
                                    {
                                        model: 'heading3',
                                        view: 'h3',
                                        title: 'Heading 3',
                                        class: 'ck-heading_heading3'
                                    }
                                ]
                            }
                        })
                        .then(newEditor => {
                            editor = newEditor;

                            // Set initial content from Livewire
                            if (@this.content) {
                                editor.setData(@this.content);
                            }

                            // Listen for changes and update Livewire
                            editor.model.document.on('change:data', () => {
                                @this.set('content', editor.getData());
                            });
                        })
                        .catch(error => {
                            console.error('CKEditor initialization error:', error);
                        });
                }
            }

            // Clean up on page unload
            window.addEventListener('beforeunload', () => {
                if (editor) {
                    editor.destroy()
                        .then(() => {
                            editor = null;
                        })
                        .catch(error => {
                            console.error('CKEditor destruction error:', error);
                        });
                }
            });
        });
    </script>
@endpush

<div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-center justify-between">
        <flux:heading>Promotional Banners Management</flux:heading>

        <div class="flex items-center gap-4">
            <flux:button variant="primary" wire:click="openCreateModal" icon="plus">
                Add New Banner
            </flux:button>
        </div>
    </div>

    <div class="flex flex-wrap gap-4 items-center justify-between">
        <div class="flex flex-wrap gap-4 items-center">
            <!-- Search -->
            <flux:input wire:model.live.debounce.300ms="search" type="search" placeholder="Search banners..."
                icon="magnifying-glass" class="w-full md:w-64" />

            <!-- Status Filter -->
            <flux:select wire:model.live="statusFilter" class="w-full md:w-40">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="scheduled">Scheduled</option>
                <option value="expired">Expired</option>
            </flux:select>

            <!-- Audience Filter -->
            <flux:select wire:model.live="audienceFilter" class="w-full md:w-40">
                <option value="">All Audiences</option>
                <option value="all">Everyone</option>
                <option value="musician">Members</option>
                <option value="client">Clients</option>
            </flux:select>
        </div>
    </div>

    <!-- Banners Table -->
    <div class="overflow-x-auto rounded-lg border border-slate-200 dark:border-slate-700">
        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead class="bg-slate-50 dark:bg-slate-800">
                <tr>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Image</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Title</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Audience
                    </th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Duration
                    </th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Status</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Actions
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-slate-200 bg-white dark:divide-slate-700 dark:bg-slate-900">
                @forelse ($banners as $banner)
                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                        <td class="px-6 py-4">
                            <div class="w-16 h-12 overflow-hidden rounded">
                                <img src="{{ $banner->image_url }}" alt="{{ $banner->title }}"
                                    class="w-full h-full object-cover">
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="font-medium text-slate-900 dark:text-slate-100">{{ $banner->title }}</div>
                            @if ($banner->link_url)
                                <div class="text-xs text-slate-500 dark:text-slate-400">
                                    <a href="{{ $banner->link_url }}" target="_blank" class="hover:underline">
                                        {{ $banner->link_text ?: 'View Link' }}
                                    </a>
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            <span
                                class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $banner->target_audience === 'all'
                                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                                    : ($banner->target_audience === 'musician'
                                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
                                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300') }}">
                                        @if($banner->target_audience === 'musician')
                                            Member
                                            @else
                                            Client
                                        @endif
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-slate-900 dark:text-slate-100">
                                @if ($banner->start_date && $banner->end_date)
                                    {{ $banner->start_date->format('M d, Y') }} -
                                    {{ $banner->end_date->format('M d, Y') }}
                                @elseif ($banner->start_date)
                                    From {{ $banner->start_date->format('M d, Y') }}
                                @elseif ($banner->end_date)
                                    Until {{ $banner->end_date->format('M d, Y') }}
                                @else
                                    No time limit
                                @endif
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            @php
                                $status = 'Inactive';
                                $badgeClasses = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';

                                if ($banner->is_active) {
                                    if ($banner->start_date && $banner->start_date->isFuture()) {
                                        $status = 'Scheduled';
                                        $badgeClasses =
                                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
                                    } elseif ($banner->end_date && $banner->end_date->isPast()) {
                                        $status = 'Expired';
                                        $badgeClasses =
                                            'bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300';
                                    } else {
                                        $status = 'Active';
                                        $badgeClasses =
                                            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
                                    }
                                }
                            @endphp

                            <span class="inline-flex rounded-full px-2 py-1 text-xs font-semibold {{ $badgeClasses }}">
                                {{ $status }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <button wire:click="toggleStatus({{ $banner->id }})"
                                    class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-slate-700 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-200 dark:hover:bg-slate-700"
                                    title="{{ $banner->is_active ? 'Deactivate' : 'Activate' }}">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="{{ $banner->is_active ? 'M6 4h4v16H6V4zm8 0h4v16h-4V4z' : 'M5 3l14 9-14 9V3z' }}" />
                                    </svg>
                                </button>
                                <button wire:click="openEditModal({{ $banner->id }})"
                                    class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-slate-700 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-200 dark:hover:bg-slate-700">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                </button>
                                <button wire:click="confirmDelete({{ $banner->id }})"
                                    class="inline-flex items-center justify-center rounded-md border border-slate-200 bg-white px-2 py-1 text-sm font-medium text-red-500 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:hover:bg-slate-700">
                                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-sm text-slate-500 dark:text-slate-400">No
                            promotional banners found</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div>
        {{ $banners->links() }}
    </div>

    <!-- Create/Edit Modal -->
    <flux:modal name="banner-modal" wire:model="isModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">
            {{ $modalMode === 'create' ? 'Create New Promotional Banner' : 'Edit Promotional Banner' }}
        </flux:heading>

        <div class="mt-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Title -->
                <div class="md:col-span-2">
                    <flux:label for="title">Title (optional)</flux:label>
                    <flux:input id="title" wire:model="title" placeholder="Banner title" />
                    @error('title')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>



                <!-- Image Upload -->
                <div class="md:col-span-2">
                    <flux:label for="image">Banner Image</flux:label>
                    <div class="mt-1 flex items-center">
                        <div x-data="{ uploading: false, progress: 0 }" x-on:livewire-upload-start="uploading = true"
                            x-on:livewire-upload-finish="uploading = false"
                            x-on:livewire-upload-error="uploading = false"
                            x-on:livewire-upload-progress="progress = $event.detail.progress">
                            <input type="file" wire:model="image" id="image" class="hidden"
                                accept="image/*">
                            <label for="image"
                                class="cursor-pointer px-4 py-2 border border-slate-300 dark:border-slate-700 rounded-md text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800">
                                Choose Image
                            </label>

                            <!-- Progress bar -->
                            <div x-show="uploading" class="mt-2">
                                <div class="h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                                    <div class="h-full bg-blue-500" :style="`width: ${progress}%`"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @error('image')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror

                    <!-- Image Preview -->
                    @if ($imagePreview)
                        <div class="mt-3">
                            <div class="text-sm font-medium mb-1">Preview:</div>
                            <div class="w-full h-40 overflow-hidden rounded-md">
                                <img src="{{ $imagePreview }}" alt="Banner preview"
                                    class="w-full h-full object-cover">
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Link URL -->
                <div>
                    <flux:label for="link_url">Link URL (optional)</flux:label>
                    <flux:input id="link_url" wire:model="link_url" placeholder="https://example.com" />
                    @error('link_url')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>



                <!-- Start Date -->
                <div>
                    <flux:label for="start_date">Start Date (optional)</flux:label>
                    <flux:input id="start_date" type="date" wire:model="start_date" />
                    @error('start_date')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <!-- End Date -->
                <div>
                    <flux:label for="end_date">End Date (optional)</flux:label>
                    <flux:input id="end_date" type="date" wire:model="end_date" />
                    @error('end_date')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Target Audience -->
                <div>
                    <flux:label for="target_audience">Target Audience</flux:label>
                    <flux:select id="target_audience" wire:model="target_audience">
                        <option value="all">Everyone</option>
                        <option value="musician">Members Only</option>
                        <option value="client">Clients Only</option>
                    </flux:select>
                    @error('target_audience')
                        <span class="text-red-500 text-sm">{{ $message }}</span>
                    @enderror
                </div>

                <!-- Active Status -->
                <div class="md:col-span-2 flex items-center space-x-2">
                    <flux:label for="is_active">Active</flux:label>
                    <flux:switch id="is_active" wire:model="is_active" />
                </div>
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
            <flux:button wire:click="$set('isModalOpen', false)">Cancel</flux:button>
            <flux:button variant="primary" wire:click="saveBanner">
                {{ $modalMode === 'create' ? 'Create Banner' : 'Update Banner' }}
            </flux:button>
        </div>
    </flux:modal>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="delete-banner" class="max-w-2xl w-full" wire:model="isDeleteModalOpen">
        <flux:heading size="lg">Confirm Deletion</flux:heading>

        <div class="my-4">
            @if ($selectedBanner)
                <p>Are you sure you want to delete the banner <strong>{{ $selectedBanner->title }}</strong>?</p>
                <p class="mt-2 text-red-500">This action cannot be undone.</p>
            @endif
        </div>

        <div class="flex justify-end space-x-3">
            <flux:button wire:click="$set('isDeleteModalOpen', false)">Cancel</flux:button>
            <flux:button variant="danger" wire:click="deleteBanner">Delete</flux:button>
        </div>
    </flux:modal>
</div>

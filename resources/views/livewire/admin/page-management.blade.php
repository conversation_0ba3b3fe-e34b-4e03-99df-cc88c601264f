<div>
    <flux:heading size="xl">Page Management</flux:heading>
    <flux:subheading>Manage dynamic content for Terms & Conditions and Privacy Policy</flux:subheading>

    <!-- Search -->
    <div class="flex flex-col sm:flex-row gap-4 mb-6 mt-10">
        <div class="flex-1">
            <flux:input wire:model.live="search" placeholder="Search pages..." icon="magnifying-glass" />
        </div>
    </div>

    <!-- Pages Table -->
    <div class="overflow-x-auto rounded-lg border border-slate-200 dark:border-slate-700">
        <table class="min-w-full divide-y divide-slate-200 dark:divide-slate-700">
            <thead class="bg-slate-50 dark:bg-slate-800">
                <tr>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Title</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Slug</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Status</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Last
                        Updated</th>
                    <th scope="col"
                        class="px-6 py-3 text-left text-sm font-semibold text-slate-900 dark:text-slate-100">Actions
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-slate-200 bg-white dark:divide-slate-700 dark:bg-slate-900">
                @forelse($pages as $page)
                    <tr class="hover:bg-slate-50 dark:hover:bg-slate-800/50">
                        <td class="px-6 py-4">
                            <div class="font-medium">{{ $page->title }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <flux:badge variant="outline">{{ $page->slug }}</flux:badge>
                        </td>
                        <td class="px-6 py-4">
                            <flux:badge variant="{{ $page->is_active ? 'success' : 'danger' }}">
                                {{ $page->is_active ? 'Active' : 'Inactive' }}
                            </flux:badge>
                        </td>
                        <td class="px-6 py-4">
                            {{ $page->updated_at->format('M d, Y H:i') }}
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center space-x-2">
                                <flux:button wire:click="editPage({{ $page->id }})" size="sm"
                                    variant="ghost" title="Edit Page">
                                    <flux:icon.pencil class="size-4" />
                                </flux:button>
                                <flux:button wire:click="toggleStatus({{ $page->id }})" size="sm"
                                    variant="ghost" title="{{ $page->is_active ? 'Deactivate' : 'Activate' }} Page">
                                    <flux:icon.{{ $page->is_active ? 'eye-slash' : 'eye' }} class="size-4" />
                                </flux:button>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="px-6 py-8 text-center">
                            <div class="text-gray-500 dark:text-gray-400">
                                <flux:icon.document-text
                                    class="size-12 mx-auto mb-4 text-gray-300 dark:text-gray-400" />
                                <p>No pages found</p>
                            </div>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

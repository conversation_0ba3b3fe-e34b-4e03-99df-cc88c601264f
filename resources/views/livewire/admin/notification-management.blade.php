<div class="space-y-6">
    <div class="flex flex-wrap gap-4 items-center justify-between">
        <flux:heading>Notification Management</flux:heading>

        <div class="flex items-center gap-2">
            <flux:button variant="outline" wire:click="markAllAsRead" icon="check">
                Mark All as Read
            </flux:button>
            <flux:button variant="outline" class="text-red-500" wire:click="confirmDeleteAll" icon="trash">
                Delete All
            </flux:button>
        </div>
    </div>

    <div class="flex flex-wrap gap-4 items-center justify-between">
        <div class="flex flex-wrap gap-4 items-center">
            <!-- Search -->
            <flux:input wire:model.live.debounce.300ms="search" type="search" placeholder="Search notifications..."
                icon="magnifying-glass" class="w-full md:w-64" />

            <!-- Type Filter -->
            <flux:select wire:model.live="typeFilter" class="w-full md:w-40">
                <option value="">All Types</option>
                <option value="personal">Personal</option>
                <option value="group">Group</option>
            </flux:select>

            <!-- Status Filter -->
            <flux:select wire:model.live="statusFilter" class="w-full md:w-40">
                <option value="">All Statuses</option>
                <option value="read">Read</option>
                <option value="unread">Unread</option>
            </flux:select>
        </div>
    </div>

    <!-- Notifications Table -->
    <div class="overflow-x-auto rounded-lg border border-slate-200 dark:border-slate-700">
        <div class="relative overflow-x-auto">
            <table class="w-full text-sm text-left rtl:text-right">
                <thead class="text-xs text-slate-700 uppercase bg-slate-50 dark:bg-slate-800 dark:text-slate-300">
                    <tr>
                        <th scope="col" class="px-6 py-3">Title</th>
                        <th scope="col" class="px-6 py-3">Recipient</th>
                        <th scope="col" class="px-6 py-3">Date</th>
                        <th scope="col" class="px-6 py-3">Status</th>
                        <th scope="col" class="px-6 py-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($notifications as $notification)
                        <tr class="bg-white border-b dark:bg-slate-900 dark:border-slate-700 {{ $notification->is_read ? '' : 'bg-blue-50 dark:bg-blue-900/10' }}">
                            <td class="px-6 py-4">
                                <div class="font-medium">{{ $notification->title }}</div>
                                <div class="text-xs text-slate-500 truncate max-w-xs">{{ $notification->body }}</div>
                            </td>
                            <td class="px-6 py-4">
                                @if ($notification->user)
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden bg-slate-200 dark:bg-slate-700 mr-3">
                                            @if ($notification->user->image_url)
                                                <img src="{{ $notification->user->image_url }}" alt="{{ $notification->user->name }}" class="h-full w-full object-cover">
                                            @else
                                                <div class="h-full w-full flex items-center justify-center text-sm font-medium">
                                                    {{ substr($notification->user->name, 0, 1) }}
                                                </div>
                                            @endif
                                        </div>
                                        <div>
                                            <div class="font-medium">{{ $notification->user->name }}</div>
                                            <div class="text-xs text-slate-500">{{ $notification->user->email }}</div>
                                        </div>
                                    </div>
                                @elseif ($notification->notification_for)
                                    <flux:badge :variant="$notification->notification_for === 'all' ? 'blue' : ($notification->notification_for === 'musician' ? 'purple' : 'green')">
                                        {{ $notification->notification_for === 'all' ? 'All Users' : ($notification->notification_for === 'musician' ? 'Members' : 'Clients') }}
                                    </flux:badge>
                                @else
                                    <span class="text-slate-500">—</span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm">{{ $notification->created_at->format('M d, Y') }}</div>
                                <div class="text-xs text-slate-500">{{ $notification->created_at->format('h:i A') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <flux:badge :variant="$notification->is_read ? 'success' : 'warning'">
                                    {{ $notification->is_read ? 'Read' : 'Unread' }}
                                </flux:badge>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    @if (!$notification->is_read)
                                        <flux:button size="xs" variant="outline" wire:click="markAsRead({{ $notification->id }})" title="Mark as Read">
                                            <flux:icon name="check" class="h-4 w-4" />
                                        </flux:button>
                                    @endif
                                    <flux:button size="xs" variant="outline" wire:click="viewNotification({{ $notification->id }})">
                                        <flux:icon name="eye" class="h-4 w-4" />
                                    </flux:button>
                                    <flux:button size="xs" variant="outline" class="text-red-500" wire:click="confirmDelete({{ $notification->id }})">
                                        <flux:icon name="trash" class="h-4 w-4" />
                                    </flux:button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr class="bg-white dark:bg-slate-900">
                            <td colspan="5" class="px-6 py-4 text-center text-slate-500 dark:text-slate-400">No notifications found</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div>
        {{ $notifications->links() }}
    </div>

    <!-- View Modal -->
    <flux:modal name="view-notification" wire:model="isViewModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">Notification Details</flux:heading>

        @if ($selectedNotification)
            <div class="mt-4 space-y-4">
                <div>
                    <div class="text-sm font-medium text-slate-500">Title</div>
                    <div class="text-lg font-semibold">{{ $selectedNotification->title }}</div>
                </div>

                <div>
                    <div class="text-sm font-medium text-slate-500">Message</div>
                    <div class="mt-1 p-3 bg-slate-50 dark:bg-slate-800 rounded-md">
                        {{ $selectedNotification->body }}
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <div class="text-sm font-medium text-slate-500">Sent On</div>
                        <div>{{ $selectedNotification->created_at->format('M d, Y h:i A') }}</div>
                    </div>

                    <div>
                        <div class="text-sm font-medium text-slate-500">Status</div>
                        <div>
                            <flux:badge :variant="$selectedNotification->is_read ? 'success' : 'warning'">
                                {{ $selectedNotification->is_read ? 'Read' : 'Unread' }}
                            </flux:badge>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="text-sm font-medium text-slate-500">Recipient</div>
                    @if ($selectedNotification->user)
                        <div class="flex items-center mt-1">
                            <div class="flex-shrink-0 h-10 w-10 rounded-full overflow-hidden bg-slate-200 dark:bg-slate-700 mr-3">
                                @if ($selectedNotification->user->image_url)
                                    <img src="{{ $selectedNotification->user->image_url }}" alt="{{ $selectedNotification->user->name }}" class="h-full w-full object-cover">
                                @else
                                    <div class="h-full w-full flex items-center justify-center text-sm font-medium">
                                        {{ substr($selectedNotification->user->name, 0, 1) }}
                                    </div>
                                @endif
                            </div>
                            <div>
                                <div class="font-medium">{{ $selectedNotification->user->name }}</div>
                                <div class="text-sm text-slate-500">{{ $selectedNotification->user->email }}</div>
                            </div>
                        </div>
                    @elseif ($selectedNotification->notification_for)
                        <div class="mt-1">
                            <flux:badge :variant="$selectedNotification->notification_for === 'all' ? 'blue' : ($selectedNotification->notification_for === 'musician' ? 'purple' : 'green')" size="lg">
                                {{ $selectedNotification->notification_for === 'all' ? 'All Users' : ($selectedNotification->notification_for === 'musician' ? 'Members' : 'Clients') }}
                            </flux:badge>
                        </div>
                    @else
                        <div class="mt-1 text-slate-500">No specific recipient</div>
                    @endif
                </div>

                @if ($selectedNotification->data)
                    <div>
                        <div class="text-sm font-medium text-slate-500">Additional Data</div>
                        <div class="mt-1 p-3 bg-slate-50 dark:bg-slate-800 rounded-md">
                            <pre class="text-xs overflow-auto">{{ json_encode($selectedNotification->data, JSON_PRETTY_PRINT) }}</pre>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        <div class="mt-6 flex justify-end space-x-3">
            <flux:button wire:click="$set('isViewModalOpen', false)">Close</flux:button>
            @if ($selectedNotification && !$selectedNotification->is_read)
                <flux:button variant="primary" wire:click="markAsRead({{ $selectedNotification->id }})">
                    Mark as Read
                </flux:button>
            @endif
        </div>
    </flux:modal>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="delete-notification" wire:model="isDeleteModalOpen" class="max-w-2xl w-full"> 
        <flux:heading size="lg">Confirm Deletion</flux:heading>

        <div class="my-4">
            @if ($selectedNotification)
                <p>Are you sure you want to delete the notification <strong>{{ $selectedNotification->title }}</strong>?</p>
                <p class="mt-2 text-red-500">This action cannot be undone.</p>
            @endif
        </div>

        <div class="flex justify-end space-x-3">
            <flux:button wire:click="$set('isDeleteModalOpen', false)">Cancel</flux:button>
            <flux:button variant="danger" wire:click="deleteNotification">Delete</flux:button>
        </div>
    </flux:modal>

    <!-- Delete All Confirmation Modal -->
    <flux:modal name="delete-all-notifications" wire:model="isDeleteAllModalOpen" class="max-w-2xl w-full">
        <flux:heading size="lg">Confirm Delete All</flux:heading>

        <div class="my-4">
            <p>Are you sure you want to delete all notifications matching your current filters?</p>
            <p class="mt-2 text-red-500">This action cannot be undone.</p>
        </div>

        <div class="flex justify-end space-x-3">
            <flux:button wire:click="$set('isDeleteAllModalOpen', false)">Cancel</flux:button>
            <flux:button variant="danger" wire:click="deleteAllNotifications">Delete All</flux:button>
        </div>
    </flux:modal>
</div>

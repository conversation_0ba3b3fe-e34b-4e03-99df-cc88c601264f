<div>
    <x-auth-header :title="__('Contact Us')" :description="__('Get in touch with our team. We\'d love to hear from you!')" />

    <div class="max-w-2xl mx-auto w-full mt-10">
        <div class="space-y-6">
            <form wire:submit="submit" class="space-y-6">
                <!-- Name -->
                <div>
                    <flux:label for="name">Full Name</flux:label>
                    <flux:input 
                        id="name" 
                        wire:model="name" 
                        type="text" 
                        placeholder="Enter your full name"
                        required 
                    />
                    @error('name')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <flux:label for="email">Email Address</flux:label>
                    <flux:input 
                        id="email" 
                        wire:model="email" 
                        type="email" 
                        placeholder="Enter your email address"
                        required 
                    />
                    @error('email')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Subject -->
                <div>
                    <flux:label for="subject">Subject</flux:label>
                    <flux:input 
                        id="subject" 
                        wire:model="subject" 
                        type="text" 
                        placeholder="What is this regarding?"
                        required 
                    />
                    @error('subject')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Message -->
                <div>
                    <flux:label for="message">Message</flux:label>
                    <flux:textarea 
                        id="message" 
                        wire:model="message" 
                        rows="6" 
                        placeholder="Please describe your inquiry in detail..."
                        required 
                    />
                    <div class="text-sm text-gray-500 mt-1">
                        Maximum 2000 characters
                    </div>
                    @error('message')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <flux:button type="submit" variant="primary" icon="paper-airplane">
                        Send Message
                    </flux:button>
                </div>
            </form>
        </div>
    </div>
</div>

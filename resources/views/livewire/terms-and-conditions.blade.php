<div>

    <style>
        .prose {
            color: #374151;
            max-width: none;
        }

        .prose h1 {
            color: #111827;
            font-weight: 800;
            font-size: 2.25rem;
            margin-top: 0;
            margin-bottom: 2rem;
        }

        .prose h2 {
            color: #111827;
            font-weight: 700;
            font-size: 1.875rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .prose h3 {
            color: #111827;
            font-weight: 600;
            font-size: 1.5rem;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .prose p {
            margin-top: 1.25rem;
            margin-bottom: 1.25rem;
            line-height: 1.75;
        }

        .prose ul,
        .prose ol {
            margin-top: 1.25rem;
            margin-bottom: 1.25rem;
            padding-left: 1.625rem;
        }

        .prose li {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .prose strong {
            color: #111827;
            font-weight: 600;
        }

        .prose a {
            color: #2563eb;
            text-decoration: underline;
            font-weight: 500;
        }

        .prose a:hover {
            color: #1d4ed8;
        }

        .prose blockquote {
            font-weight: 500;
            font-style: italic;
            color: #111827;
            border-left-width: 0.25rem;
            border-left-color: #e5e7eb;
            quotes: "\201C" "\201D" "\2018" "\2019";
            margin-top: 1.6rem;
            margin-bottom: 1.6rem;
            padding-left: 1rem;
        }

        .prose table {
            width: 100%;
            table-layout: auto;
            text-align: left;
            margin-top: 2rem;
            margin-bottom: 2rem;
            font-size: 0.875rem;
            line-height: 1.7142857;
        }

        .prose thead {
            border-bottom-width: 1px;
            border-bottom-color: #d1d5db;
        }

        .prose thead th {
            color: #111827;
            font-weight: 600;
            vertical-align: bottom;
            padding-right: 0.5714286rem;
            padding-bottom: 0.5714286rem;
            padding-left: 0.5714286rem;
        }

        .prose tbody tr {
            border-bottom-width: 1px;
            border-bottom-color: #e5e7eb;
        }

        .prose tbody td {
            vertical-align: baseline;
            padding-top: 0.5714286rem;
            padding-right: 0.5714286rem;
            padding-bottom: 0.5714286rem;
            padding-left: 0.5714286rem;
        }
    </style>

    <div class="max-w-4xl mx-auto p-6">
        <div class="prose prose-lg max-w-none">
            {!! $page->content !!}
        </div>

        <!-- Back to Home -->
        <div class="mt-8 text-center">
            <flux:button variant="ghost" onclick="window.history.back()">
                <flux:icon.arrow-left class="size-4" />
                Go Back
            </flux:button>
        </div>
    </div>
</div>

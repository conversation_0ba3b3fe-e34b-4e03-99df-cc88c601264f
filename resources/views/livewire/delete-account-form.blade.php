<div>
    <x-auth-header :title="__('Delete Account Request')" :description="__('We\'re sorry to see you go. Please fill out this form to request account deletion.')" />

    <div class="max-w-2xl mx-auto">
        <!-- Warning Notice -->
        <div class="mb-6 border-yellow-200 bg-yellow-50 p-3 rounded-md mt-5">
            <div class="flex items-start space-x-3">
                <flux:icon.exclamation-triangle class="size-6 text-yellow-600 mt-1" />
                <div>
                    <flux:heading size="sm" class="text-yellow-800">Important Notice</flux:heading>
                    <p class="text-yellow-700 mt-1">
                        Account deletion is permanent and cannot be undone. All your data, including profile information, 
                        messages, and account history will be permanently removed from our systems.
                    </p>
                </div>
            </div>
        </div>

        <div class="space-y-6">
            <form wire:submit="submit" class="space-y-6">
                <!-- Name -->
                <div>
                    <flux:label for="name">Full Name</flux:label>
                    <flux:input 
                        id="name" 
                        wire:model="name" 
                        type="text" 
                        placeholder="Enter your full name"
                        required 
                    />
                    @error('name')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Email -->
                <div>
                    <flux:label for="email">Email Address</flux:label>
                    <flux:input 
                        id="email" 
                        wire:model="email" 
                        type="email" 
                        placeholder="Enter your registered email address"
                        required 
                    />
                    @error('email')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Reason for Deletion -->
                <div>
                    <flux:label for="reason">Reason for Deletion</flux:label>
                    <flux:select id="reason" wire:model="reason" placeholder="Please select a reason" required>
                        @foreach($this->getReasonOptions() as $value)
                            <flux:select.option value="{{ $value }}">{{ $value }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    @error('reason')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Additional Feedback -->
                <div>
                    <flux:label for="additional_feedback">Additional Feedback (Optional)</flux:label>
                    <flux:textarea 
                        id="additional_feedback" 
                        wire:model="additional_feedback" 
                        rows="4" 
                        placeholder="Please share any additional feedback that might help us improve our service..."
                    />
                    <div class="text-sm text-gray-500 mt-1">
                        Maximum 1000 characters
                    </div>
                    @error('additional_feedback')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </div>

                <!-- Confirmation -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <flux:icon.exclamation-circle class="size-5 text-red-600 mt-0.5" />
                        <div class="text-red-800">
                            <p class="font-medium">By submitting this form, you confirm that:</p>
                            <ul class="list-disc list-inside mt-2 space-y-1 text-sm">
                                <li>You understand that account deletion is permanent</li>
                                <li>You have backed up any important data</li>
                                <li>You want to permanently delete your account</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-3">
                    <flux:button type="button" variant="ghost" onclick="window.history.back()">
                        Cancel
                    </flux:button>
                    <flux:button type="submit" variant="danger" icon="trash">
                        Submit Deletion Request
                    </flux:button>
                </div>
            </form>
        </div>

        <!-- Process Information -->
        <div class="mt-8 bg-gray-100 dark:bg-gray-800 p-3 rounded-md">
            <flux:heading size="lg" class="mb-4">What Happens Next?</flux:heading>
            
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 text-sm font-medium">1</span>
                    </div>
                    <div>
                        <div class="font-medium">Request Review</div>
                        <div class="text-gray-600 dark:text-gray-400">Our team will review your deletion request within 24-48 hours.</div>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 text-sm font-medium">2</span>
                    </div>
                    <div>
                        <div class="font-medium">Account Deletion</div>
                        <div class="text-gray-600 dark:text-gray-400">Your account and all associated data will be permanently removed.</div>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 text-sm font-medium">3</span>
                    </div>
                    <div>
                        <div class="font-medium">Confirmation</div>
                        <div class="text-gray-600 dark:text-gray-400">You'll receive a confirmation email once the deletion is complete.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\ChatController;
use App\Http\Controllers\API\ContentController;
use App\Http\Controllers\API\MusicianController;
use App\Http\Controllers\API\SubscriptionController;
use Illuminate\Support\Facades\Route;

Route::controller(AuthController::class)->group(function () {
    Route::post('login', 'login');
    Route::post('register', 'register');
    Route::post('verify-otp', 'verifyOtp');
    Route::post('resend-otp', 'resendOtp');
    Route::post('forgot-password', 'forgotPassword');
    Route::post('verify-reset-otp', 'verifyResetOtp');
    Route::post('reset-password', 'resetPassword');
    Route::post('google-login', 'googleLogin');
});

Route::middleware(['auth:sanctum'])->group(function () {
    Route::controller(AuthController::class)->group(function () {
        Route::get('profile', 'getProfile');
        Route::get('logout', 'logout');
        Route::post('update-fcm-token', 'updateFcmToken');
        Route::post('update-profile', 'updateProfile');
        Route::post('change-password', 'changePassword');
        Route::post('update-image', 'updateImage');
        Route::post('update-banner-image', 'updateBannerImage');
        Route::get('delete-account', 'deleteAccount');
        Route::post('update-availability', 'updateAvailability');
        Route::post('update-live-location-status', 'updateLiveLocationStatus');
        Route::post('add-gallery-images', 'addGalleryImages');
        Route::get('delete-gallery-image/{galleryId}', 'deleteGalleryImage');
    });

    Route::prefix('musician')->group(function () {
        Route::controller(MusicianController::class)->group(function () {
            Route::post('add-rating', 'addRating');
            Route::post('favorite/{musicianId}', 'toggleFavorite');
            Route::get('favorites', 'getFavorites');
        });
    });

    Route::prefix('chats')->group(function () {
        Route::controller(ChatController::class)->group(function () {
            Route::get('/', 'getChats');
            Route::get('messages/{userId}', 'getMessages');
            Route::post('send-message', 'sendMessage');
            Route::get('total-unread', 'getTotalUnreadMessages');
        });
    });

    Route::prefix('subscriptions')->group(function () {
        Route::controller(SubscriptionController::class)->group(function () {
            Route::get('current', 'getSubscription');
            Route::post('subscribe', 'subscribe');
            Route::post('cancel', 'cancelSubscription');
            Route::get('toggle-auto-billing', 'toggleAutoBilling');
            Route::post('resume', 'resumeSubscription');
            Route::get('transactions', 'getTransactions');
            Route::post('web-urls', 'getSubscriptionUrls');
        });
    });
});

Route::prefix('musician')->group(function () {
    Route::controller(MusicianController::class)->group(function () {
        Route::get('profile/{musicianId}', 'getProfile');
        Route::post('contact', 'contactMusician');
        Route::post('filter', 'filterMusicians');
        Route::post('filter-nearby', 'filterNearbyMusicians');
        Route::post('top', 'getTopMusicians');
        Route::post('top-by-role', 'getTopMusiciansByRole');
        Route::post('comparison', 'getMusiciansComparison');
        Route::get('ratings/{musicianId}', 'getRatings');
    })->middleware('optional.auth');
});

Route::prefix('subscriptions')->group(function () {
    Route::controller(SubscriptionController::class)->group(function () {
        Route::get('plans/{userType?}', 'getPlans');
    })->middleware('optional.auth');
});

Route::prefix('content')->group(function () {
    Route::controller(ContentController::class)->group(function () {
        Route::get('promotional-banners', 'getPromotionalBanners');
        Route::get('notifications', 'getNotifications');
        Route::get('notifications/unread-count', 'getUnreadNotificationsCount');
        Route::post('notifications/clear', 'clearNotifications');
    })->middleware('optional.auth');
});

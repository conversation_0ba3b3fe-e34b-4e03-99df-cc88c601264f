<?php

use App\Livewire\Admin\Dashboard;
use App\Livewire\Admin\NotificationManagement;
use App\Livewire\Admin\NotificationSender;
use App\Livewire\Admin\PageEdit;
use App\Livewire\Admin\PageManagement;
use App\Livewire\Admin\PromotionalBannerManagement;
use App\Livewire\Admin\SubscriptionPlanManagement;
use App\Livewire\Admin\UserForm;
use App\Livewire\Admin\UsersManagement;
use App\Livewire\Admin\UserSubscriptionManagement;
use App\Livewire\Auth\Login;
use App\Livewire\ContactForm;
use App\Livewire\DeleteAccountForm;
use App\Livewire\PrivacyPolicy;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use App\Livewire\TermsAndConditions;
use Illuminate\Support\Facades\Route;

Route::get('/', Login::class)->name('home')->middleware('guest:admin');

// Public pages
Route::get('contact', ContactForm::class)->name('contact');
Route::get('delete-account', DeleteAccountForm::class)->name('delete-account');
Route::get('terms-and-conditions', TermsAndConditions::class)->name('terms-and-conditions');
Route::get('privacy-policy', PrivacyPolicy::class)->name('privacy-policy');

Route::get('dashboard', Dashboard::class)
    ->middleware(['auth:admin', 'verified'])
    ->name('dashboard');



Route::middleware(['auth:admin'])->group(function () {
    Route::get('admin/users', UsersManagement::class)->name('users-management');
    Route::get('admin/users/create', UserForm::class)->name('users.create');
    Route::get('admin/users/{userId}/edit', UserForm::class)->name('users.edit');
    Route::get('admin/subscription-plans', SubscriptionPlanManagement::class)->name('subscription-plans-management');
    Route::get('admin/subscriptions-management', UserSubscriptionManagement::class)->name('subscription-management');
    Route::get('admin/promotional-banners', PromotionalBannerManagement::class)->name('promotional-banners-management');
    Route::get('admin/notifications/send', NotificationSender::class)->name('notification-sender');
    Route::get('admin/notifications/manage', NotificationManagement::class)->name('notification-management');
    Route::get('admin/pages', PageManagement::class)->name('admin.pages.index');
    Route::get('admin/pages/{page}/edit', PageEdit::class)->name('admin.pages.edit');

    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});

// Mobile app subscription web views
Route::prefix('mobile')->group(function () {
    Route::get('subscribe/{token}/{planId}', [App\Http\Controllers\Mobile\SubscriptionController::class, 'showSubscriptionForm'])->name('mobile.subscribe');
    Route::post('subscribe/{token}/{planId}', [App\Http\Controllers\Mobile\SubscriptionController::class, 'processSubscription'])->name('mobile.process-subscription');
    Route::get('subscription/success/{token}', [App\Http\Controllers\Mobile\SubscriptionController::class, 'subscriptionSuccess'])->name('mobile.subscription-success');
    Route::get('subscription/cancel/{token}', [App\Http\Controllers\Mobile\SubscriptionController::class, 'cancelSubscription'])->name('mobile.cancel-subscription');
});

require __DIR__ . '/auth.php';

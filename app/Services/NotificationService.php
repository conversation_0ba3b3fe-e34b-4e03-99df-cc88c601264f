<?php

namespace App\Services;

use App\Events\UnreadNotificationsCountUpdated;
use App\Traits\FirebaseDatabaseTrait;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    // use FirebaseDatabaseTrait;

    /**
     * Send a notification to specific users or user groups
     *
     * @param string $title The notification title
     * @param string $body The notification body
     * @param array|null $userIds Array of user IDs or null
     * @param string|null $notificationFor 'all', 'musician', 'client' or null
     * @param array|null $data Additional data for the notification
     * @return array Result with count of notifications sent
     */
    public function sendNotification(string $title, string $body, ?array $userIds = null, ?string $notificationFor = null, ?array $data = null): array
    {
        $count = 0;

        try {
            // If specific users are selected
            if ($userIds && count($userIds) > 0) {
                foreach ($userIds as $userId) {
                    $user = User::find($userId);
                    if ($user) {
                        // Create notification record
                        Notification::create([
                            'title' => $title,
                            'body' => $body,
                            'user_id' => $userId,
                            'notification_for' => null, // Specific user, so no group
                            'data' => $data,
                        ]);

                        // Send FCM notification if user has FCM token
                        // $this->updateUnreadCount('unread_notifications', $userId);
                        broadcast(new UnreadNotificationsCountUpdated($userId))->toOthers();
                        if ($user->fcm_token) {
                            $this->sendFcmNotification($user->fcm_token, $title, $body, $data);
                        }

                        $count++;
                    }
                }
            }
            // If sending to a group or all users
            else if ($notificationFor) {
                // Create a single notification record for the group
                Notification::create([
                    'title' => $title,
                    'body' => $body,
                    'user_id' => null,
                    'notification_for' => $notificationFor,
                    'data' => $data,
                ]);

                // Query users based on the notification_for value
                $users = User::query()
                    ->when($notificationFor !== 'all', function ($query) use ($notificationFor) {
                        $query->where('role', $notificationFor);
                    })
                    ->whereNotNull('fcm_token')
                    ->get();

                // Send FCM notifications to all matching users
                foreach ($users as $user) {
                    // $this->updateUnreadCount('unread_notifications', $user->id);
                    broadcast(new UnreadNotificationsCountUpdated($user->id))->toOthers();
                    if ($user->fcm_token) {
                        $this->sendFcmNotification($user->fcm_token, $title, $body, $data);
                        $count++;
                    }
                }
            }

            return [
                'success' => true,
                'count' => $count,
                'message' => "Successfully sent notification to {$count} users"
            ];
        } catch (\Exception $e) {
            Log::error('Error sending notification: ' . $e->getMessage());

            return [
                'success' => false,
                'count' => $count,
                'message' => 'Error sending notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send FCM notification to a specific device
     *
     * @param string $token FCM token
     * @param string $title Notification title
     * @param string $body Notification body
     * @param array|null $data Additional data
     * @return bool Success status
     */
    private function sendFcmNotification(string $token, string $title, string $body, ?array $data = null): bool
    {
        try {

            FCMService::sendNotification($token, $title, $body);

            return true;
        } catch (\Exception $e) {
            Log::error('Error sending FCM notification: ' . $e->getMessage());
            return false;
        }
    }
}

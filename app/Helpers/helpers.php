<?php

use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Drivers\Gd\Encoders\WebpEncoder;
use Intervention\Image\ImageManager;

// json response helper
function jsonResponse($status, $data = [], $statusCode = 200)
{
    return response()->json(array_merge([
        'status' => $status,
    ], $data), $statusCode);
}

// validation error response helper
function validationError($errors = [], $message = 'Validation Error', $statusCode = 200)
{
    // If errors are provided, get the first error message.
    if (!empty($errors) && is_object($errors) && $errors->first()) {
        $message = $errors->first(); // Set the message to the first error
    }

    return response()->json([
        'status' => false,
        'message' => $message,
        'errors' => $errors
    ], $statusCode);
}


function uploadFile($file, $folder = 'uploads', $oldFile = null, $quality = 40)
{
    if (!$file) {
        return null;
    }

    // Delete old file if exists
    if ($oldFile && Storage::disk('public')->exists("$folder/$oldFile")) {
        Storage::disk('public')->delete("$folder/$oldFile");
    }

    // Get the file extension
    $extension = strtolower($file->getClientOriginalExtension());

    // Define image formats that need compression
    $compressibleFormats = ['png', 'jpg', 'jpeg', 'webp'];

    // Generate unique filename with the original extension
    $fileName = time() . uniqid() . '.' . $extension;

    // If the file is an image, compress and convert to WebP
    if (in_array($extension, $compressibleFormats)) {
        $manager = new ImageManager(Driver::class);
        $processedImage = $manager->read($file->getRealPath())
            ->encode(new WebpEncoder(quality: $quality));

        $fileName = time() . uniqid() . '.webp'; // Save as WebP
        Storage::disk('public')->put("$folder/$fileName", $processedImage);
    } else {
        // Upload non-image files as they are
        Storage::disk('public')->putFileAs($folder, $file, $fileName);
    }

    return $fileName;
}

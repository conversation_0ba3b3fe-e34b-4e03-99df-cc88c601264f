<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Mail\OtpMail;
use App\Mail\PasswordResetMail;
use App\Models\Message;
use App\Models\MusicianProfile;
use App\Models\MusicianGallery;
use App\Models\Notification;
use App\Models\OtpVerification;
use App\Models\PasswordReset;
use App\Models\Subscription;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class AuthController extends Controller
{
    public function register(Request $request)
    {
        // Validate the incoming request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6|max:255',
            'role' => 'required|in:musician,client',
        ], [
            'email.unique' => 'This email is already registered. Use a different email or register a new account.',
            'role.in' => 'Invalid role. Please choose a role: musician or client'
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }


        // Create a new user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
        ]);

        // Generate OTP
        $otp = rand(100000, 999999);
        OtpVerification::updateOrCreate(
            ['email' => $user->email],
            ['otp' => $otp]
        );

        // Send OTP email
        Mail::to($user->email)->send(new OtpMail($otp, $user));

        return jsonResponse(true, [
            'message' => 'Signup successful, please enter OTP to verify account',
            'user_id' => $user->id
        ]);
    }

    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'otp' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        // Delete expired OTP records older than 1 hour
        OtpVerification::where('created_at', '<', now()->subHour())->delete();

        $user = User::find($request->user_id);
        // Fetch the OTP record for the user
        $otpRecord = OtpVerification::where('email', $user->email)
            ->where('otp', $request->otp)
            ->first();

        if (!$otpRecord) {
            return jsonResponse(false, ['message' => 'Invalid OTP or OTP expired']);
        }

        // Mark user as verified
        $user->is_verified = 1;
        $user->save();

        // Delete OTP record after successful verification
        $otpRecord->delete();

        if ($user->role == "musician") {
            MusicianProfile::create([
                'user_id' => $user->id,
            ]);
        }

        $user->load('musicianProfile', 'subscription:id,user_id,status,trial_ends_at,ends_at,next_billing_date,cancel_at_period_end');

        // override the loaded relationship for VIP users
        if ($user->is_vip_user && !$user->subscription) {
            // Create a new Subscription model instance for VIP users
            $vipSubscription = new Subscription([
                'user_id' => $user->id,
                'status' => 'active',
                'trial_ends_at' => null,
                'ends_at' => null,
                'next_billing_date' => null,
                'cancel_at_period_end' => false,
            ]);

            // Set the attributes that aren't fillable
            $vipSubscription->id = 0;
            $vipSubscription->exists = false; // Mark as not persisted to database

            $user->setRelation('subscription', $vipSubscription);
        }


        return jsonResponse(true, [
            'message' => 'Account verified successfully',
            'token' => $user->createToken('auth_token')->plainTextToken,
            'user' => $user
        ]);
    }

    public function login(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|exists:users,email',
            'password' => 'required|string|min:6',
        ], [
            'email.exists' => 'This email is not registered. Use a different email or register a new account.'
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        // Find user by email or username
        $user = User::where('email', $request->email)
            ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return jsonResponse(false, ['message' => 'Incorrect Password!']);
        }

        if (!$user->is_active) {
            return jsonResponse(false, ['message' => 'Your account is not active. Please contact admin for support.']);
        }

        // Check if user is verified
        if (!$user->is_verified) {
            // Generate new OTP
            $otp = rand(100000, 999999);
            OtpVerification::updateOrCreate(
                ['email' => $user->email],
                ['otp' => $otp]
            );

            // Send OTP email
            Mail::to($user->email)->send(new OtpMail($otp, $user));

            return jsonResponse(true, [
                'message' => 'Account not verified. OTP sent to your email. Please verify your account.',
                'user' => $user
            ]);
        }

        // Generate token
        $token = $user->createToken('auth_token')->plainTextToken;

        $user->load(['musicianProfile', 'subscription:id,user_id,status,trial_ends_at,ends_at,next_billing_date,cancel_at_period_end']);

        // override the loaded relationship for VIP users
        if ($user->is_vip_user && !$user->subscription) {
            // Create a new Subscription model instance for VIP users
            $vipSubscription = new Subscription([
                'user_id' => $user->id,
                'status' => 'active',
                'trial_ends_at' => null,
                'ends_at' => null,
                'next_billing_date' => null,
                'cancel_at_period_end' => false,
            ]);

            // Set the attributes that aren't fillable
            $vipSubscription->id = 0;
            $vipSubscription->exists = false; // Mark as not persisted to database

            $user->setRelation('subscription', $vipSubscription);
        }

        return jsonResponse(true, [
            'message' => 'Login successful',
            'token' => $token,
            'user' => $user
        ]);
    }

    public function resendOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        // Find the user
        $user = User::find($request->user_id);

        if (!$user) {
            return jsonResponse(false, ['message' => 'User not found']);
        }

        // Delete existing OTPs for the user
        OtpVerification::where('email', $user->email)->delete();

        // Generate a new OTP
        $otp = rand(100000, 999999);
        OtpVerification::create([
            'email' => $user->email,
            'otp' => $otp,
        ]);

        // Send OTP email
        Mail::to($user->email)->send(new OtpMail($otp, $user));

        return jsonResponse(true, [
            'message' => 'OTP resent successfully.',
            'user' => $user
        ]);
    }

    public function forgotPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255|exists:users,email',
        ], [
            'email.exists' => 'This email is not registered. Use a different email or register a new account.'
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            // Generate a 6-digit OTP
            $otp = rand(100000, 999999);

            // Save the OTP in the password_resets table
            PasswordReset::updateOrCreate(
                ['email' => $request->email],
                ['token' => $otp, 'created_at' => Carbon::now()]
            );

            // Send the OTP to the user's email
            Mail::to($request->email)->send(new PasswordResetMail($otp, $request->email));


            return jsonResponse(true, [
                'message' => 'OTP sent to your email.',
            ]);
        } catch (Exception $e) {
            return jsonResponse(false, [
                'message' => 'Forgot Password Failed due to: ' . $e->getMessage()
            ], 500);
        }
    }


    public function verifyResetOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|max:255|exists:users,email',
            'otp' => 'required|string|digits:6',
        ], [
            'email.exists' => 'This email is not registered. Use a different email or register a new account.'
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        // Delete expired tokens
        PasswordReset::where('email', $request->email)
            ->where('created_at', '<', Carbon::now()->subMinutes(15))
            ->delete();

        // Fetch OTP from database
        $passwordReset = PasswordReset::where('email', $request->email)->first();

        if (!$passwordReset) {
            return jsonResponse(false, [
                'message' => 'Invalid or expired OTP.'
            ]);
        }

        if ($passwordReset->token !== $request->otp) {
            return jsonResponse(false, [
                'message' => 'Invalid OTP.'
            ]);
        }

        // Fetch user details
        $user = User::where('email', $request->email)->first();

        return jsonResponse(true, [
            'message' => 'OTP verified successfully.',
            'user' => $user
        ]);
    }



    public function resetPassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'email' => 'required|string|email|max:255|exists:users,email',
                'password' => 'required|string|min:6|max:255',
            ], [
                'email.exists' => 'This email is not registered. Use a different email or register a new account.'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // Fetch the user
            $user = User::where('id', $request->user_id)->where('email', $request->email)->first();

            if (!$user) {
                return jsonResponse(false, [
                    'message' => 'User not found.'
                ]);
            }

            // Update password
            $user->password = Hash::make($request->password);
            $user->save();

            // Delete OTP record
            PasswordReset::where('email', $request->email)->delete();

            return jsonResponse(true, [
                'message' => 'Password reset successfully.'
            ]);
        } catch (Exception $e) {
            return jsonResponse(false, [
                'message' => 'Reset Password Failed due to: ' . $e->getMessage()
            ], 500);
        }
    }


    public function logout()
    {
        auth()->user()->update(['fcm_token' => null]);
        auth()->user()->tokens()->delete();
        return jsonResponse(true, [
            'message' => 'Logout successfull!',
        ]);
    }

    public function getProfile()
    {
        $user = auth()->user();

        $user->load(
            $user->role === 'musician'
                ? ['musicianProfile.galleryImages', 'subscription:id,user_id,status,trial_ends_at,ends_at,next_billing_date,cancel_at_period_end']
                : ['subscription:id,user_id,status,trial_ends_at,ends_at,next_billing_date,cancel_at_period_end']
        );

        // ✅ Key fix: override the loaded relationship for VIP users
        if ($user->is_vip_user && !$user->subscription) {
            // Create a new Subscription model instance for VIP users
            $vipSubscription = new Subscription([
                'user_id' => $user->id,
                'status' => 'active',
                'trial_ends_at' => null,
                'ends_at' => null,
                'next_billing_date' => null,
                'cancel_at_period_end' => false,
            ]);

            // Set the attributes that aren't fillable
            $vipSubscription->id = 0;
            $vipSubscription->exists = false; // Mark as not persisted to database

            $user->setRelation('subscription', $vipSubscription);
        }

        return jsonResponse(true, [
            'user' => $user,
        ]);
    }


    public function updateFcmToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fcm_token' => 'required',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $user = auth()->user();
        $userId = $user->id;
        $userRole = $user->role;

        $user->fcm_token = $request->fcm_token;
        $user->save();

        // Get cleared notification IDs for this user
        $clearedNotificationIds = \App\Models\ClearedNotification::where('user_id', $userId)
            ->pluck('notification_id')
            ->toArray();

        // Count unread notifications for this specific user
        // OR notifications for all users or users with this role (excluding cleared ones)
        $count = Notification::where('is_read', 0)
            ->where(function ($query) use ($userId, $userRole, $clearedNotificationIds) {
                $query->where('user_id', $userId)
                    ->orWhere(function ($q) use ($userRole, $clearedNotificationIds) {
                        $q->whereNull('user_id')
                            ->where(function ($q) use ($userRole) {
                                $q->where('notification_for', 'all')
                                    ->orWhere('notification_for', $userRole);
                            })
                            ->whereNotIn('id', $clearedNotificationIds);
                    });
            })
            ->count();



        $totalUnread = Message::whereHas('chat', function ($query) use ($userId) {
            $query->where('client_id', $userId)
                ->orWhere('musician_id', $userId);
        })->where('is_read', 0)
            ->where('sender_id', '!=', $userId) // Only count unread incoming messages
            ->count();

        $user->load('musicianProfile', 'subscription:id,user_id,status,trial_ends_at,ends_at,next_billing_date,cancel_at_period_end');

        // override the loaded relationship for VIP users
        if ($user->is_vip_user && !$user->subscription) {
            // Create a new Subscription model instance for VIP users
            $vipSubscription = new Subscription([
                'user_id' => $user->id,
                'status' => 'active',
                'trial_ends_at' => null,
                'ends_at' => null,
                'next_billing_date' => null,
                'cancel_at_period_end' => false,
            ]);

            // Set the attributes that aren't fillable
            $vipSubscription->id = 0;
            $vipSubscription->exists = false; // Mark as not persisted to database

            $user->setRelation('subscription', $vipSubscription);
        }



        return jsonResponse(true, [
            'message' => 'FCM token updated successfully!',
            'user' => $user,
            'notifications_unread_count' => $count,
            'chats_unread_count' => $totalUnread
        ]);
    }

    public function updateProfile(Request $request)
    {
        $user = auth()->user();

        // Basic validation rules
        $rules = [
            'name' => 'nullable|string|max:255',
            'brand_name' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:10240',
        ];

        // Additional rules for musician profile
        if ($user->role == "musician") {
            $rules = array_merge($rules, [
                'phone_number' => 'nullable|string|max:20',
                'country_code' => 'nullable|string|max:20',
                'website' => 'nullable|max:255',
                'description' => 'nullable|string|max:2000',
                'header_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:10240',
                'roles' => 'nullable|array',
                'offered_services' => 'nullable|array',
                'instruments' => 'nullable|array',
                'music_types' => 'nullable|array',
                'spoken_languages' => 'nullable|array',
                'payment_methods' => 'nullable|array',
                'rate_per_hour' => 'nullable|numeric',
                'rate_per_event' => 'nullable|numeric',
                'social_links' => 'nullable|array',
                'social_links.*.key' => 'nullable|string|max:50',
                'social_links.*.value' => 'nullable|string|max:255',
                'tags' => 'nullable|array',
                'location' => 'nullable|string|max:255',
            ]);
        }

        // Validate request
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        // Update user fields
        if ($request->filled('name')) {
            $user->name = $request->name;
        }

        if ($request->has('brand_name')) {
            $user->brand_name = $request->brand_name;
        }

        if ($request->file('image')) {
            $user->image = uploadFile($request->file('image'), 'profile_photos', $user->image);
        }

        if ($request->longitude) {
            $user->longitude = $request->longitude;
        }

        if ($request->latitude) {
            $user->latitude = $request->latitude;
        }

        $user->save();

        // Update musician-specific fields
        if ($user->role == "musician") {
            $musicianProfile = MusicianProfile::firstOrCreate(['user_id' => $user->id]);

            $fillableData = [];

            // Handle regular fields
            $regularFields = [
                'phone_number',
                'website',
                'description',
                'rate_per_hour',
                'rate_per_event',
                'location',
                'country_code'
            ];

            foreach ($regularFields as $field) {
                if ($request->filled($field)) {
                    $fillableData[$field] = $request->input($field);
                }
            }

            // Handle array fields
            $arrayFields = [
                'roles',
                'offered_services',
                'instruments',
                'music_types',
                'spoken_languages',
                'payment_methods',
                'tags',
                'social_links'
            ];

            foreach ($arrayFields as $field) {
                if ($request->has($field)) {
                    if ($field === 'social_links') {
                        // Filter out empty social links
                        $socialLinks = $request->input($field) ?? [];
                        $filteredLinks = [];

                        foreach ($socialLinks as $socialLink) {
                            if (!empty($socialLink['key']) && !empty($socialLink['value'])) {
                                $filteredLinks[] = [
                                    'key' => $socialLink['key'],
                                    'value' => $socialLink['value']
                                ];
                            }
                        }

                        $fillableData[$field] = $filteredLinks;
                    } else {
                        $fillableData[$field] = $request->input($field) ?? [];
                    }
                }
            }

            // Handle header image upload
            if ($request->hasFile('header_image')) {
                $fillableData['header_image'] = uploadFile(
                    $request->file('header_image'),
                    'musician_header_images',
                    $musicianProfile->header_image
                );
            }

            $musicianProfile->fill($fillableData);
            $musicianProfile->save();

            // Check if musician profile is completed
            if ($musicianProfile->isProfileCompleted()) {
                $user->is_profile_completed = true;
                $user->save();
            }
        }

        return jsonResponse(true, [
            'message' => 'Profile updated successfully',
            'user' => $user->load('musicianProfile.galleryImages')
        ]);
    }



    public function updateImage(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'image' => 'required|image|mimes:jpeg,png,jpg,webp|max:10240', // Image validation
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // Get authenticated user
            $user = auth()->user();

            // Upload and update image
            if ($request->hasFile('image')) {
                $user->image = uploadFile($request->file('image'), 'profile_photos', $user->image);
                $user->save();
            }

            return jsonResponse(true, [
                'message' => 'Profile image updated successfully.',
                'user' => $user->load('musicianProfile'),
            ]);
        } catch (Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to update profile image: ' . $e->getMessage()
            ], 500);
        }
    }

    public function updateBannerImage(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'header_image' => 'required|image|mimes:jpeg,png,jpg,webp|max:10240',
            ]);

            // Get authenticated user
            $user = auth()->user();

            // Ensure user has a musician profile
            if (!$user->musicianProfile) {
                return jsonResponse(false, ['message' => 'Musician profile not found.'], 404);
            }

            // Upload and update image
            if ($request->hasFile('header_image')) {
                $bannerImagePath = uploadFile($request->file('header_image'), 'banner_images', $user->musicianProfile->banner_image);
                $user->musicianProfile->update(['header_image' => $bannerImagePath]);
            }

            return jsonResponse(true, [
                'message' => 'Banner image updated successfully.',
                'user' => $user->load('musicianProfile'),
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to update banner image.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    public function changePassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'old_password' => 'required|string|min:6|max:255',
                'new_password' => 'required|string|min:6|max:255',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // Get the authenticated user
            $user = auth()->user();

            // Check if old password is correct
            if (!Hash::check($request->old_password, $user->password)) {
                return jsonResponse(false, [
                    'message' => 'Old password is incorrect.'
                ]);
            }

            // Update the password
            $user->password = Hash::make($request->new_password);
            $user->save();

            return jsonResponse(true, [
                'message' => 'Password changed successfully.'
            ]);
        } catch (Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to change password: ' . $e->getMessage()
            ]);
        }
    }


    public function googleLogin(Request $request)
    {
        // Validate the request input
        $validator = Validator::make($request->all(), [
            'oauth_token' => 'required'
        ]);

        // if validation fails
        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $oauthToken = $request->oauth_token;
            $socialUser = Socialite::driver('google')->userFromToken($oauthToken);

            if ($socialUser) {
                $user = User::where('email', $socialUser->email)
                    ->first();

                if ($user) {

                    if (!$user->is_verified) {
                        $user->update([
                            'is_verified' => 1
                        ]);
                    }

                    auth()->login($user);

                    $token = $user->createToken('auth_token')->plainTextToken;

                    return jsonResponse(true, [
                        'message' => 'Login successful',
                        'token' => $token,
                        'user' => $user->load('musicianProfile', 'subscription:id,user_id,status,trial_ends_at,ends_at,next_billing_date,cancel_at_period_end'),
                        'user_exists' => true
                    ]);
                } else {
                    if ($request->role) {

                        $validator = Validator::make($request->all(), [
                            'role' => 'required|in:musician,client',
                        ]);

                        if ($validator->fails()) {
                            return validationError($validator->errors());
                        }


                        // **Handle Google Profile Image**
                        $profileImage = null;
                        if ($socialUser->avatar) {
                            $imageData = @file_get_contents($socialUser->avatar);
                            if ($imageData) {
                                $tempFile = tempnam(sys_get_temp_dir(), 'google_avatar');
                                file_put_contents($tempFile, $imageData);

                                // Convert image into Laravel file format
                                $uploadedFile = new UploadedFile(
                                    $tempFile,
                                    'google_avatar.jpg',
                                    mime_content_type($tempFile),
                                    null,
                                    true
                                );

                                $profileImage = uploadFile($uploadedFile, 'profile_photos'); // Save in `storage/app/public/users/`
                            }
                        }

                        $firstName = $socialUser->user['given_name'] ?? null;
                        $lastName = $socialUser->user['family_name'] ?? null;


                        $user = User::create([
                            'name' => $firstName . ' ' . $lastName,
                            'email' => $socialUser->email,
                            'password' => Hash::make(Str::random(10)),
                            'role' => $request->role,
                            'is_verified' => 1,
                            'image' => $profileImage,
                        ]);

                        auth()->login($user);

                        $token = $user->createToken('auth_token')->plainTextToken;

                        if ($user->role == "musician") {
                            MusicianProfile::create([
                                'user_id' => $user->id,
                            ]);
                        }


                        return jsonResponse(true, [
                            'message' => 'Register successful',
                            'token' => $token,
                            'user' => $user->load('musicianProfile', 'subscription:id,user_id,status,trial_ends_at,ends_at,next_billing_date,cancel_at_period_end'),
                            'user_exists' => true
                        ]);
                    }
                    return jsonResponse(false, ['message' => 'User not found.', 'user_exists' => false]);
                }
            } else {
                return jsonResponse(false, ['message' => 'Social login failed.']);
            }
        } catch (\Exception $e) {
            return jsonResponse(false, ['message' => 'Social login failed due to error: ' . $e->getMessage()]);
        }
    }



    public function deleteAccount()
    {
        $user = auth()->user();

        // Delete user's data
        if ($user->role == "musician") {
            $user->musicianProfile()->delete();
        }
        $user->delete();

        return jsonResponse(true, [
            'message' => 'Account deleted successfully!',
        ]);
    }

    public function updateAvailability(Request $request)
    {
        $user = auth()->user();
        $user->availability = (int) $request->availability;
        $user->save();

        return jsonResponse(true, [
            'message' => 'Availability updated successfully!',
            'availability' => $user->availability
        ]);
    }

    public function updateLiveLocationStatus(Request $request)
    {
        $user = auth()->user();
        $user->live_location = (int) $request->live_location;
        if ($request->longitude) {
            $user->longitude = $request->longitude;
        }
        if ($request->latitude) {
            $user->latitude = $request->latitude;
        }
        $user->save();

        return jsonResponse(true, [
            'message' => 'Live location ' . ($request->live_location ? 'enabled' : 'disabled') . ' successfully!',
            'live_location' => $user->live_location
        ]);
    }

    public function addGalleryImages(Request $request)
    {
        try {
            $user = auth()->user();

            // Check if user is a musician
            if ($user->role !== 'musician') {
                return jsonResponse(false, ['message' => 'Only musicians can add gallery images.'], 403);
            }

            // Ensure user has a musician profile
            if (!$user->musicianProfile) {
                return jsonResponse(false, ['message' => 'Musician profile not found.'], 404);
            }

            // Check current gallery count
            $currentCount = $user->musicianProfile->galleryImages()->count();

            // Validate request
            $validator = Validator::make($request->all(), [
                'images' => 'required|array|min:1',
                'images.*' => 'required|image|mimes:jpeg,png,jpg,webp|max:10240',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $images = $request->file('images');
            $newImagesCount = count($images);

            // Check if adding new images would exceed the limit
            if ($currentCount + $newImagesCount > 20) {
                return jsonResponse(false, [
                    'message' => 'Cannot add images. Maximum 20 images allowed. Current: ' . $currentCount . ', Trying to add: ' . $newImagesCount
                ]);
            }

            $uploadedImages = [];

            foreach ($images as $image) {
                $imagePath = uploadFile($image, 'musician_gallery');

                $galleryImage = MusicianGallery::create([
                    'musician_profile_id' => $user->musicianProfile->id,
                    'image' => $imagePath,
                ]);

                $uploadedImages[] = $galleryImage;
            }

            return jsonResponse(true, [
                'message' => count($uploadedImages) . ' image(s) added to gallery successfully.',
                'images' => $uploadedImages,
                'total_images' => $currentCount + count($uploadedImages)
            ]);
        } catch (Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to add gallery images: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteGalleryImage($galleryId)
    {
        try {
            $user = auth()->user();

            // Check if user is a musician
            if ($user->role !== 'musician') {
                return jsonResponse(false, ['message' => 'Only musicians can delete gallery images.'], 403);
            }

            // Validate request
            $validator = Validator::make(['gallery_id' => $galleryId], [
                'gallery_id' => 'required|integer|exists:musician_galleries,id',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // Find the gallery image
            $galleryImage = MusicianGallery::where('id', $galleryId)
                ->whereHas('musicianProfile', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })
                ->first();

            if (!$galleryImage) {
                return jsonResponse(false, ['message' => 'Gallery image not found or you do not have permission to delete it.'], 404);
            }

            // Delete the image file from storage
            if ($galleryImage->image && Storage::disk('public')->exists('musician_gallery/' . $galleryImage->image)) {
                Storage::disk('public')->delete('musician_gallery/' . $galleryImage->image);
            }

            // Delete the gallery record
            $galleryImage->delete();

            return jsonResponse(true, [
                'message' => 'Gallery image deleted successfully.',
                'remaining_images' => $user->musicianProfile->galleryImages()->count()
            ]);
        } catch (Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to delete gallery image: ' . $e->getMessage()
            ], 500);
        }
    }
}

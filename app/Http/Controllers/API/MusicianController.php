<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Mail\MusicianContactMail;
use App\Models\MusicianProfile;
use App\Models\Rating;
use App\Models\User;
use App\Models\FavoriteMusician;
use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class MusicianController extends Controller
{

    public function filterMusicians(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'nullable|string',
                'location' => 'nullable|string',
                'city' => 'nullable|string',
                'country' => 'nullable|string',
                'roles' => 'nullable|array',
                'offered_services' => 'nullable|array',
                'instruments' => 'nullable|array',
                'music_types' => 'nullable|array',
                'spoken_languages' => 'nullable|array',
                'ratings' => 'nullable|array',
                'radius' => 'nullable'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // Check if at least one filter is applied
            // if (!$this->hasFilters($request)) {
            //     return jsonResponse(true, [
            //         'musicians' => [],
            //         'pagination' => [
            //             'total' => 0,
            //             'per_page' => 10,
            //             'current_page' => 1,
            //             'last_page' => 0,
            //         ]
            //     ]);
            // }


            // Start with base query
            $query = User::where('role', 'musician')
                ->where('is_active', 1)
                ->where('is_verified', 1)
                ->where('is_live', 1)
                ->where('is_profile_completed', 1)
                ->with('musicianProfile')
                ->where(function ($q) {
                    $q->where('is_vip_user', true)
                        ->orWhereHas('activeSubscription');
                });

            // Add distance calculation if coordinates are provided
            if ($request->filled('longitude') && $request->filled('latitude')) {
                $longitude = $request->input('longitude');
                $latitude = $request->input('latitude');

                $query->selectRaw("*, 
                ST_Distance_Sphere(
                    point(longitude, latitude),
                    point(?, ?)
                ) * 0.001 as distance_km", [$longitude, $latitude])
                    ->whereNotNull('longitude')
                    ->whereNotNull('latitude');

                // Apply radius filter if provided
                if ($request->filled('radius')) {
                    $radius = $request->input('radius');
                    $query->whereRaw("ST_Distance_Sphere(
                        point(longitude, latitude),
                        point(?, ?)
                    ) * 0.001 <= ?", [$longitude, $latitude, $radius]);
                }

                $query->orderBy('distance_km');
            }


            // Search query
            if ($request->filled('query')) {
                $searchTerm = $request->input('query');
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'like', "%$searchTerm%")
                        ->orWhereHas('musicianProfile', function ($q) use ($searchTerm) {
                            $q->where('location', 'like', "%$searchTerm%")
                                ->orWhere('description', 'like', "%$searchTerm%")
                                ->orWhere('roles', 'like', "%$searchTerm%")
                                ->orWhere('offered_services', 'like', "%$searchTerm%")
                                ->orWhere('instruments', 'like', "%$searchTerm%")
                                ->orWhere('music_types', 'like', "%$searchTerm%")
                                ->orWhere('spoken_languages', 'like', "%$searchTerm%");
                        });
                });
            }

            // Location filter
            if ($request->filled('location')) {
                $query->whereHas('musicianProfile', function ($q) use ($request) {
                    $q->where('location', 'like', "%$request->location%");
                });
            }

            if ($request->filled('city') || $request->filled('country')) {
                $query->whereHas('musicianProfile', function ($q) use ($request) {
                    if ($request->filled('city')) {
                        $q->where('location', 'like', "%$request->city%");
                    } else if ($request->filled('country')) {
                        $q->where('location', 'like', "%$request->country%");
                    }
                });
            }

            // Array filters
            $arrayFilters = [
                'roles',
                'offered_services',
                'instruments',
                'music_types',
                'spoken_languages'
            ];

            foreach ($arrayFilters as $filter) {
                $input = $request->input($filter);

                // Check if it's a non-empty array with at least one truthy value
                if (is_array($input) && count(array_filter($input))) {
                    $query->whereHas('musicianProfile', function ($q) use ($filter, $input) {
                        $q->whereJsonContains($filter, $input);
                    });
                }
            }


            // Ratings filter
            if ($request->filled('ratings')) {
                $ratings = $request->input('ratings');
                if (is_array($ratings) && count(array_filter($ratings))) {
                    $query->whereHas('musicianProfile', function ($q) use ($ratings) {
                        $q->whereIn('average_rating', $ratings);
                    });
                }
            }

            $musicians = $query->paginate(10);
            $userId = auth('sanctum')->id() ?? null;

            $formattedMusicians = $musicians->map(function ($musician) use ($userId) {
                return [
                    'id' => $musician->id,
                    'name' => $musician->name,
                    'image_url' => $musician->image_url,
                    'description' => $musician->musicianProfile?->description,
                    'location' => $musician->musicianProfile?->location,
                    'rate_per_hour' => $musician->musicianProfile?->rate_per_hour,
                    'rate_per_event' => $musician->musicianProfile?->rate_per_event,
                    'roles' => $musician->musicianProfile?->roles,
                    'tags' => $musician->musicianProfile?->tags,
                    'longitude' => $musician->longitude,
                    'latitude' => $musician->latitude,
                    'average_rating' => $musician->musicianProfile?->average_rating,
                    'ratings_count' => $musician->musicianProfile?->ratings_count,
                    'availability' => $musician->availability,
                    'is_favorite' => FavoriteMusician::where('user_id', $userId)
                        ->where('musician_id', $musician->id)
                        ->exists()
                ];
            });

            return jsonResponse(true, [
                'musicians' => $formattedMusicians,
                'pagination' => [
                    'total' => $musicians->total(),
                    'per_page' => $musicians->perPage(),
                    'current_page' => $musicians->currentPage(),
                    'last_page' => $musicians->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to filter musicians: ' . $e->getMessage()
            ]);
        }
    }

    private function hasFilters(Request $request): bool
    {
        $filterFields = [
            'query',
            'location',
            'roles',
            'offered_services',
            'instruments',
            'music_types',
            'spoken_languages',
            'ratings'
        ];

        foreach ($filterFields as $field) {
            if ($request->filled($field)) {
                return true;
            }
        }

        return false;
    }

    public function filterNearbyMusicians(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'nullable|string',
                'location' => 'nullable|string',
                'city' => 'nullable|string',
                'country' => 'nullable|string',
                'roles' => 'nullable|array',
                'offered_services' => 'nullable|array',
                'instruments' => 'nullable|array',
                'music_types' => 'nullable|array',
                'spoken_languages' => 'nullable|array',
                'ratings' => 'nullable|array',
                'longitude' => 'nullable|numeric',
                'latitude' => 'nullable|numeric',
                'radius' => 'nullable' // Add radius validation
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // Check if at least one filter is applied
            if (!$this->hasFilters($request) && !($request->filled('longitude') && $request->filled('latitude'))) {
                return jsonResponse(true, [
                    'musicians' => [],
                    'pagination' => [
                        'total' => 0,
                        'per_page' => 10,
                        'current_page' => 1,
                        'last_page' => 0,
                    ]
                ]);
            }
            // Start with base query
            $query = User::where('role', 'musician')
                ->where('is_active', 1)
                ->where('is_verified', 1)
                ->where('is_live', 1)
                ->where('is_profile_completed', 1)
                ->with('musicianProfile')
                ->where(function ($q) {
                    $q->where('is_vip_user', true)
                        ->orWhereHas('activeSubscription');
                });
            // Add distance calculation if coordinates are provided
            if ($request->filled('longitude') && $request->filled('latitude')) {
                $longitude = $request->input('longitude');
                $latitude = $request->input('latitude');

                $query->selectRaw("*, 
                ST_Distance_Sphere(
                    point(longitude, latitude),
                    point(?, ?)
                ) * 0.001 as distance_km", [$longitude, $latitude])
                    ->whereNotNull('longitude')
                    ->whereNotNull('latitude');

                // Apply radius filter if provided
                if ($request->filled('radius')) {
                    $radius = $request->input('radius');
                    $query->whereRaw("ST_Distance_Sphere(
                        point(longitude, latitude),
                        point(?, ?)
                    ) * 0.001 <= ?", [$longitude, $latitude, $radius]);
                }

                $query->orderBy('distance_km');
            }

            // Search query
            if ($request->filled('query')) {
                $searchTerm = $request->input('query');
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'like', "%$searchTerm%")
                        ->orWhereHas('musicianProfile', function ($q) use ($searchTerm) {
                            $q->where('location', 'like', "%$searchTerm%")
                                ->orWhere('description', 'like', "%$searchTerm%")
                                ->orWhere('roles', 'like', "%$searchTerm%")
                                ->orWhere('offered_services', 'like', "%$searchTerm%")
                                ->orWhere('instruments', 'like', "%$searchTerm%")
                                ->orWhere('music_types', 'like', "%$searchTerm%")
                                ->orWhere('spoken_languages', 'like', "%$searchTerm%");
                        });
                });
            }

            // Location filter
            if ($request->filled('location')) {
                $query->whereHas('musicianProfile', function ($q) use ($request) {
                    $q->where('location', 'like', "%$request->location%");
                });
            }

            if ($request->filled('city') || $request->filled('country')) {
                $query->whereHas('musicianProfile', function ($q) use ($request) {
                    if ($request->filled('city')) {
                        $q->where('location', 'like', "%$request->city%");
                    } else if ($request->filled('country')) {
                        $q->where('location', 'like', "%$request->country%");
                    }
                });
            }



            // Array filters
            $arrayFilters = [
                'roles',
                'offered_services',
                'instruments',
                'music_types',
                'spoken_languages'
            ];

            foreach ($arrayFilters as $filter) {
                if ($request->filled($filter) && is_array($request->input($filter)) && count(array_filter($request->input($filter)))) {
                    $query->whereHas('musicianProfile', function ($q) use ($filter, $request) {
                        $q->whereJsonContains($filter, $request->input($filter));
                    });
                }
            }

            // Ratings filter
            if ($request->filled('ratings') && is_array($request->input('ratings')) && count(array_filter($request->input('ratings')))) {
                $query->whereHas('musicianProfile', function ($q) use ($request) {
                    $q->whereIn('average_rating', $request->ratings);
                });
            }

            $musicians = $query->paginate(10);
            $userId = auth('sanctum')->id() ?? null;

            $formattedMusicians = $musicians->map(function ($musician) use ($request, $userId) {
                $data = [
                    'id' => $musician->id,
                    'name' => $musician->name,
                    'image_url' => $musician->image_url,
                    'description' => $musician->musicianProfile?->description,
                    'location' => $musician->musicianProfile?->location,
                    'rate_per_hour' => $musician->musicianProfile?->rate_per_hour,
                    'rate_per_event' => $musician->musicianProfile?->rate_per_event,
                    'roles' => $musician->musicianProfile?->roles,
                    'tags' => $musician->musicianProfile?->tags,
                    'longitude' => $musician->longitude,
                    'latitude' => $musician->latitude,
                    'average_rating' => $musician->musicianProfile?->average_rating,
                    'ratings_count' => $musician->musicianProfile?->ratings_count,
                    'availability' => $musician->availability,
                    'is_favorite' => FavoriteMusician::where('user_id', $userId)
                        ->where('musician_id', $musician->id)
                        ->exists()
                ];

                // Add distance if coordinates were provided
                if ($request->filled('longitude') && $request->filled('latitude')) {
                    $data['distance_km'] = round($musician->distance_km, 2);
                }

                return $data;
            });

            return jsonResponse(true, [
                'musicians' => $formattedMusicians,
                'pagination' => [
                    'total' => $musicians->total(),
                    'per_page' => $musicians->perPage(),
                    'current_page' => $musicians->currentPage(),
                    'last_page' => $musicians->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to filter nearby musicians: ' . $e->getMessage()
            ]);
        }
    }

    public function getTopMusicians(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'country' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $userId = auth('sanctum')->id() ?? null;

            $query = User::where('role', 'musician')
                ->where('is_active', 1)
                ->where('is_verified', 1)
                ->where('is_live', 1)
                ->where('is_profile_completed', 1)
                ->where(function ($q) {
                    $q->where('is_vip_user', true)
                        ->orWhereHas('activeSubscription');
                })
                ->with('musicianProfile');
            // ->when($userId, function ($query) use ($userId) {
            //     $query->where('id', '!=', $userId);
            // });


            if ($request->filled('country')) {
                // Use a raw query to prioritize musicians from the requested country
                $query->select('users.*')
                    ->join('musician_profiles', 'users.id', '=', 'musician_profiles.user_id')
                    ->orderByRaw(
                        "CASE WHEN musician_profiles.location LIKE ? THEN 0 ELSE 1 END",
                        ["%{$request->country}%"]
                    )
                    ->orderBy('musician_profiles.average_rating', 'desc')
                    ->orderBy('musician_profiles.ratings_count', 'desc');
            } else {
                // If no country specified, just sort by ratings
                $query->whereHas('musicianProfile', function ($q) {
                    $q->orderBy('average_rating', 'desc')
                        ->orderBy('ratings_count', 'desc');
                });
            }

            $musicians = $query->paginate(10);

            $formattedMusicians = $musicians->map(function ($musician) use ($userId) {
                return [
                    'id' => $musician->id,
                    'name' => $musician->name,
                    'image_url' => $musician->image_url,
                    'description' => $musician->musicianProfile?->description,
                    'location' => $musician->musicianProfile?->location,
                    'rate_per_hour' => $musician->musicianProfile?->rate_per_hour,
                    'rate_per_event' => $musician->musicianProfile?->rate_per_event,
                    'roles' => $musician->musicianProfile?->roles,
                    'tags' => $musician->musicianProfile?->tags,
                    'longitude' => $musician->longitude,
                    'latitude' => $musician->latitude,
                    'average_rating' => $musician->musicianProfile?->average_rating,
                    'ratings_count' => $musician->musicianProfile?->ratings_count,
                    'availability' => $musician->availability,
                    'is_favorite' => FavoriteMusician::where('user_id', $userId)
                        ->where('musician_id', $musician->id)
                        ->exists()
                ];
            });

            return jsonResponse(true, [
                'musicians' => $formattedMusicians,
                'pagination' => [
                    'total' => $musicians->total(),
                    'per_page' => $musicians->perPage(),
                    'current_page' => $musicians->currentPage(),
                    'last_page' => $musicians->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch top musicians: ' . $e->getMessage()
            ]);
        }
    }

    public function getTopMusiciansByRole(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'roles' => 'nullable|array',
                'longitude' => 'nullable|numeric',
                'latitude' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $roles = $request->input('roles');
            $userId = auth('sanctum')->id() ?? null;

            // Add distance calculation if coordinates are provided
            if ($request->filled('longitude') && $request->filled('latitude')) {
                $longitude = $request->input('longitude');
                $latitude = $request->input('latitude');

                // First get musicians with location data
                $queryWithLocation = User::where('role', 'musician')
                    ->where('is_active', 1)
                    ->where('is_verified', 1)
                    ->where('is_live', 1)
                    ->where('is_profile_completed', 1)
                    ->with('musicianProfile')
                    ->where(function ($q) {
                        $q->where('is_vip_user', true)
                            ->orWhereHas('activeSubscription');
                    })

                    // ->when($userId, fn ($q) => $q->where('id', '!=', $userId))
                    ->whereNotNull('longitude')
                    ->whereNotNull('latitude')
                    ->selectRaw("users.*, 
                        ST_Distance_Sphere(
                            point(users.longitude, users.latitude),
                            point(?, ?)
                        ) * 0.001 as distance_km", [$longitude, $latitude])
                    ->join('musician_profiles', 'users.id', '=', 'musician_profiles.user_id')
                    ->when($roles, function ($q) use ($roles) {
                        $q->where(function ($subQuery) use ($roles) {
                            foreach ($roles as $role) {
                                $subQuery->orWhere('musician_profiles.roles', 'like', "%$role%");
                            }
                        });
                    })
                    ->orderBy('distance_km');

                // Then get musicians without location data
                $queryWithoutLocation = User::where('role', 'musician')
                    ->where('is_active', 1)
                    ->where('is_verified', 1)
                    ->where('is_live', 1)
                    ->where('is_profile_completed', 1)
                    ->where(function ($q) {
                        $q->whereNull('longitude')
                            ->orWhereNull('latitude');
                    })
                    // ->when($userId, fn ($q) => $q->where('id', '!=', $userId))
                    ->selectRaw("users.*, NULL as distance_km")
                    ->join('musician_profiles', 'users.id', '=', 'musician_profiles.user_id')
                    ->when($roles, function ($q) use ($roles) {
                        $q->where(function ($subQuery) use ($roles) {
                            foreach ($roles as $role) {
                                $subQuery->orWhere('musician_profiles.roles', 'like', "%$role%");
                            }
                        });
                    })
                    ->with('musicianProfile')
                    ->where(function ($q) {
                        $q->where('is_vip_user', true)
                            ->orWhereHas('activeSubscription');
                    });

                // Combine the queries
                $musicians = $queryWithLocation->union($queryWithoutLocation)->paginate(10);
            } else {
                // If no coordinates specified, just filter by role
                $query = User::where('role', 'musician')
                    ->where('is_active', 1)
                    ->where('is_verified', 1)
                    ->where('is_live', 1)
                    ->where('is_profile_completed', 1)
                    ->when($roles, function ($q) use ($roles) {
                        $q->whereHas('musicianProfile', function ($q) use ($roles) {
                            $q->where(function ($subQuery) use ($roles) {
                                foreach ($roles as $role) {
                                    $subQuery->orWhere('roles', 'like', "%$role%");
                                }
                            });
                        });
                    })
                    ->with('musicianProfile')
                    ->where(function ($q) {
                        $q->where('is_vip_user', true)
                            ->orWhereHas('activeSubscription');
                    });

                $musicians = $query->paginate(10);
            }


            $formattedMusicians = $musicians->map(function ($musician) use ($request, $userId) {
                $data = [
                    'id' => $musician->id,
                    'name' => $musician->name,
                    'image_url' => $musician->image_url,
                    'description' => $musician->musicianProfile?->description,
                    'location' => $musician->musicianProfile?->location,
                    'rate_per_hour' => $musician->musicianProfile?->rate_per_hour,
                    'rate_per_event' => $musician->musicianProfile?->rate_per_event,
                    'roles' => $musician->musicianProfile?->roles,
                    'tags' => $musician->musicianProfile?->tags,
                    'longitude' => $musician->longitude,
                    'latitude' => $musician->latitude,
                    'average_rating' => $musician->musicianProfile?->average_rating,
                    'ratings_count' => $musician->musicianProfile?->ratings_count,
                    'availability' => $musician->availability,
                    'is_favorite' => FavoriteMusician::where('user_id', $userId)
                        ->where('musician_id', $musician->id)
                        ->exists()
                ];

                // Add distance if coordinates were provided and musician has location
                if (
                    $request->filled('longitude') && $request->filled('latitude') &&
                    $musician->longitude && $musician->latitude
                ) {
                    $data['distance_km'] = round($musician->distance_km, 2);
                }

                return $data;
            });

            return jsonResponse(true, [
                'musicians' => $formattedMusicians,
                'pagination' => [
                    'total' => $musicians->total(),
                    'per_page' => $musicians->perPage(),
                    'current_page' => $musicians->currentPage(),
                    'last_page' => $musicians->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch top musicians by role: ' . $e->getMessage()
            ]);
        }
    }

    public function getMusiciansComparison(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'musician_ids' => 'required|array|min:1',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $userId = auth('sanctum')->id() ?? null;

            $musicians = User::whereIn('id', $request->musician_ids)
                ->where('role', 'musician')
                ->where('is_active', 1)
                ->where('is_verified', 1)
                ->where('is_live', 1)
                ->where('is_profile_completed', 1)
                ->with('musicianProfile')
                ->where(function ($q) {
                    $q->where('is_vip_user', true)
                        ->orWhereHas('activeSubscription');
                })

                // ->when($userId, function ($query) use ($userId) {
                //     $query->where('id', '!=', $userId);
                // })
                ->get();

            $formattedMusicians = $musicians->map(function ($musician) use ($userId) {
                return [
                    'id' => $musician->id,
                    'name' => $musician->name,
                    'image_url' => $musician->image_url,
                    'description' => $musician->musicianProfile?->description,
                    'location' => $musician->musicianProfile?->location,
                    'rate_per_hour' => $musician->musicianProfile?->rate_per_hour,
                    'rate_per_event' => $musician->musicianProfile?->rate_per_event,
                    'longitude' => $musician->longitude,
                    'latitude' => $musician->latitude,
                    'average_rating' => $musician->musicianProfile?->average_rating,
                    'ratings_count' => $musician->musicianProfile?->ratings_count,
                    'roles' => $musician->musicianProfile?->roles,
                    'offered_services' => $musician->musicianProfile?->offered_services,
                    'instruments' => $musician->musicianProfile?->instruments,
                    'music_types' => $musician->musicianProfile?->music_types,
                    'spoken_languages' => $musician->musicianProfile?->spoken_languages,
                    'payment_methods' => $musician->musicianProfile?->payment_methods,
                    'tags' => $musician->musicianProfile?->tags,
                    'availability' => $musician->availability,
                    'is_favorite' => FavoriteMusician::where('user_id', $userId)
                        ->where('musician_id', $musician->id)
                        ->exists()
                ];
            });

            return jsonResponse(true, [
                'musicians' => $formattedMusicians
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch musicians comparison: ' . $e->getMessage()
            ]);
        }
    }

    public function getProfile($musicianId)
    {
        try {
            $musician = User::with(['musicianProfile', 'musicianProfile.galleryImages', 'musicianProfile.ratings' => function ($query) {
                $query->latest('id')->limit(5);
            }, 'musicianProfile.ratings.user:id,name,image'])
                ->where('id', $musicianId)
                ->first();

            if (!$musician) {
                return jsonResponse(false, [
                    'message' => 'Musician not found'
                ]);
            }
            if (auth('sanctum')->check()) {
                $musician->is_favorite = FavoriteMusician::where('user_id', auth('sanctum')->id())
                    ->where('musician_id', $musician->id)
                    ->exists();

                $musician->rating_exists = Rating::where('user_id', auth('sanctum')->id())
                    ->where('musician_profile_id', $musician->musicianProfile->id)
                    ->exists();
            }

            return jsonResponse(true, [
                'musician' => $musician
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch musician profile: ' . $e->getMessage()
            ]);
        }
    }

    public function addRating(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'musician_profile_id' => 'required',
            'rating' => 'required|numeric|min:1|max:5',
            'comment' => 'nullable|string|max:2000'
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $ratingUser = auth('sanctum')->user();

            $rating = Rating::create([
                'user_id' => $ratingUser->id,
                'musician_profile_id' => $request->musician_profile_id,
                'rating' => $request->rating,
                'comment' => $request->comment
            ]);

            // Update average rating and count for the musician profile
            $musicianProfile = MusicianProfile::with('user')->find($request->musician_profile_id);
            $musicianProfile->update([
                'average_rating' => $musicianProfile->ratings?->avg('rating'),
                'ratings_count' => $musicianProfile->ratings?->count()
            ]);

            // Send push notification to the musician
            $musician = $musicianProfile->user;
            if ($musician && $musician->fcm_token) {
                $title = 'New Rating Received';
                $body = $ratingUser->name . ' rated you ' . $request->rating . ' stars' .
                    ($request->comment ? ': "' . substr($request->comment, 0, 50) . (strlen($request->comment) > 50 ? '..."' : '"') : '');

                FCMService::sendNotification(
                    $musician->fcm_token,
                    $title,
                    $body
                );
            }

            return jsonResponse(true, [
                'message' => 'Rating added successfully',
                'rating' => $rating
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to add rating: ' . $e->getMessage()
            ]);
        }
    }

    public function getRatings($musicianId)
    {
        try {
            $ratings = Rating::with('user:id,name,image')
                ->where('musician_profile_id', $musicianId)
                ->latest('id')
                ->paginate(10);

            $ratingsData = $ratings->map(function ($rating) {
                return [
                    'id' => $rating->id,
                    'rating' => $rating->rating,
                    'comment' => $rating->comment,
                    'created_at' => $rating->created_at,
                    'user' => [
                        'id' => $rating->user?->id,
                        'name' => $rating->user?->name,
                        'image_url' => $rating->user?->image_url
                    ]
                ];
            });

            return jsonResponse(true, [
                'ratings' => $ratingsData,
                'pagination' => [
                    'total' => $ratings->total(),
                    'per_page' => $ratings->perPage(),
                    'current_page' => $ratings->currentPage(),
                    'last_page' => $ratings->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch ratings',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function toggleFavorite($musicianId)
    {
        try {
            $userId = auth('sanctum')->id();

            // Check if musician exists and is actually a musician
            $musician = User::where('id', $musicianId)
                ->where('role', 'musician')
                ->where('is_active', 1)
                ->first();

            if (!$musician) {
                return jsonResponse(false, [
                    'message' => 'Musician not found'
                ], 404);
            }

            // Check if already favorited
            $favorite = FavoriteMusician::where('user_id', $userId)
                ->where('musician_id', $musicianId)
                ->first();

            if ($favorite) {
                // If exists, remove from favorites
                $favorite->delete();
                $message = 'Removed from favorites';
                $is_favorite = false;
            } else {
                // If doesn't exist, add to favorites
                FavoriteMusician::create([
                    'user_id' => $userId,
                    'musician_id' => $musicianId
                ]);
                $message = 'Added to favorites';
                $is_favorite = true;
            }

            return jsonResponse(true, [
                'message' => $message,
                'is_favorite' => $is_favorite
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to toggle favorite: ' . $e->getMessage()
            ]);
        }
    }

    public function getFavorites()
    {
        try {
            $favorites = FavoriteMusician::where('user_id', auth('sanctum')->id())
                ->with(['musician' => function ($q) {
                    $q->with('musicianProfile');
                }])
                ->paginate(10);

            $formattedFavorites = $favorites->map(function ($favorite) {
                $musician = $favorite->musician;
                return [
                    'id' => $musician->id,
                    'name' => $musician->name,
                    'image_url' => $musician->image_url,
                    'description' => $musician->musicianProfile?->description,
                    'location' => $musician->musicianProfile?->location,
                    'rate_per_hour' => $musician->musicianProfile?->rate_per_hour,
                    'rate_per_event' => $musician->musicianProfile?->rate_per_event,
                    'roles' => $musician->musicianProfile?->roles,
                    'tags' => $musician->musicianProfile?->tags,
                    'longitude' => $musician->longitude,
                    'latitude' => $musician->latitude,
                    'average_rating' => $musician->musicianProfile?->average_rating,
                    'ratings_count' => $musician->musicianProfile?->ratings_count,
                    'availability' => $musician->availability,
                    'is_favorite' => true,
                    'favorited_at' => $favorite->created_at?->diffForHumans()

                ];
            });

            return jsonResponse(true, [
                'favorites' => $formattedFavorites,
                'pagination' => [
                    'total' => $favorites->total(),
                    'per_page' => $favorites->perPage(),
                    'current_page' => $favorites->currentPage(),
                    'last_page' => $favorites->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch favorites: ' . $e->getMessage()
            ]);
        }
    }

    public function contactMusician(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'musician_id' => 'required',
                'name' => 'required|string|max:255',
                'email' => 'required|string|max:50',
                'phone' => 'nullable|string|max:20',
                'message' => 'nullable|string|max:2000'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $musician = User::where('id', $request->musician_id)->first();

            if (!$musician) {
                return jsonResponse(false, [
                    'message' => 'Musician not found'
                ]);
            }

            // Send notification to musician
            $title = 'New Contact Request';
            $body = $request->name . ' has contacted you. Please check your email for details and respond promptly.';

            if ($musician->fcm_token) {
                FCMService::sendNotification(
                    $musician->fcm_token,
                    $title,
                    $body
                );
            }

            // Send email to musician
            Mail::to($musician->email)->send(new MusicianContactMail($request->all(), $musician->name));

            return jsonResponse(true, [
                'message' => 'Message sent successfully'
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to send message: ' . $e->getMessage()
            ]);
        }
    }
}

<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\PromotionalBanner;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ContentController extends Controller
{
    /**
     * Get promotional banners for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPromotionalBanners()
    {
        try {
            $user = Auth::guard('sanctum')->user();
            $userRole = $user->role;

            // Get active banners that match the user's role or are for all users
            $banners = PromotionalBanner::active()
                ->forAudience($userRole)
                ->get();

            return jsonResponse(true, [
                'banners' => $banners
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch promotional banners: ' . $e->getMessage()
            ],);
        }
    }

    /**
     * Get notifications for the authenticated user and mark them as read
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNotifications()
    {
        try {
            $user = Auth::guard('sanctum')->user();
            $userId = $user->id;
            $userRole = $user->role;

            // Get cleared notification IDs for this user
            $clearedNotificationIds = \App\Models\ClearedNotification::where('user_id', $userId)
                ->pluck('notification_id')
                ->toArray();

            // Get notifications for this specific user
            // OR notifications for all users or users with this role (excluding cleared ones)
            $notifications = Notification::where(function ($query) use ($userId, $userRole, $clearedNotificationIds) {
                $query->where('user_id', $userId)
                    ->orWhere(function ($q) use ($userRole, $clearedNotificationIds) {
                        $q->whereNull('user_id')
                            ->where(function ($q) use ($userRole) {
                                $q->where('notification_for', 'all')
                                    ->orWhere('notification_for', $userRole);
                            })
                            ->whereNotIn('id', $clearedNotificationIds);
                    });
            })
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            // Mark all retrieved notifications as read
            foreach ($notifications as $notification) {
                if (!$notification->is_read) {
                    $notification->markAsRead();
                }
            }

            return jsonResponse(true, [
                'notifications' => $notifications->items(),
                'pagination' => [
                    'total' => $notifications->total(),
                    'per_page' => $notifications->perPage(),
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch notifications: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unread notifications count for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnreadNotificationsCount()
    {
        try {
            $user = Auth::guard('sanctum')->user();
            $userId = $user->id;
            $userRole = $user->role;

            // Get cleared notification IDs for this user
            $clearedNotificationIds = \App\Models\ClearedNotification::where('user_id', $userId)
                ->pluck('notification_id')
                ->toArray();

            // Count unread notifications for this specific user
            // OR notifications for all users or users with this role (excluding cleared ones)
            $count = Notification::where('is_read', false)
                ->where(function ($query) use ($userId, $userRole, $clearedNotificationIds) {
                    $query->where('user_id', $userId)
                        ->orWhere(function ($q) use ($userRole, $clearedNotificationIds) {
                            $q->whereNull('user_id')
                                ->where(function ($q) use ($userRole) {
                                    $q->where('notification_for', 'all')
                                        ->orWhere('notification_for', $userRole);
                                })
                                ->whereNotIn('id', $clearedNotificationIds);
                        });
                })
                ->count();

            return jsonResponse(true, [
                'unread_count' => $count
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to fetch unread notifications count: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete all notifications for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */

    public function clearNotifications()
    {
        try {
            $user = Auth::guard('sanctum')->user();
            $userId = $user->id;
            $userRole = $user->role;

            // Delete all notifications for this specific user
            Notification::where('user_id', $userId)->delete();

            // For all/role notifications, mark them as cleared for this user
            $allRoleNotifications = Notification::whereNull('user_id')
                ->where(function ($query) use ($userRole) {
                    $query->where('notification_for', 'all')
                        ->orWhere('notification_for', $userRole);
                })
                ->get();

            foreach ($allRoleNotifications as $notification) {
                \App\Models\ClearedNotification::firstOrCreate([
                    'notification_id' => $notification->id,
                    'user_id' => $userId
                ]);
            }

            return jsonResponse(true, [
                'message' => 'Notifications cleared successfully'
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => 'Failed to delete notifications: ' . $e->getMessage()
            ], 500);
        }
    }
}

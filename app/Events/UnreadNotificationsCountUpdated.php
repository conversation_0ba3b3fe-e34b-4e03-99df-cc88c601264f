<?php

namespace App\Events;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UnreadNotificationsCountUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;


    public $userId;
    public $unreadCount;

    public function __construct($userId)
    {
        $this->userId = $userId;

        $userRole = User::find($userId)?->role;

        // Get cleared notification IDs for this user
        $clearedNotificationIds = \App\Models\ClearedNotification::where('user_id', $userId)
            ->pluck('notification_id')
            ->toArray();

        // Count unread notifications for this specific user
        $this->unreadCount = Notification::where('is_read', 0)->where(function ($query) use ($userId, $userRole, $clearedNotificationIds) {
            $query->where('user_id', $userId)
                ->orWhere(function ($q) use ($userRole, $clearedNotificationIds) {
                    $q->whereNull('user_id')
                        ->where(function ($q) use ($userRole) {
                            $q->where('notification_for', 'all')
                                ->orWhere('notification_for', $userRole);
                        })
                        ->whereNotIn('id', $clearedNotificationIds);
                });
        })->count();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */

    public function broadcastOn(): array
    {
        return [new Channel('user.' . $this->userId)];
    }

    public function broadcastWith(): array
    {
        return [
            'unread_count' => $this->unreadCount
        ];
    }
}

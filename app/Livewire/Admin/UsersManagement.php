<?php

namespace App\Livewire\Admin;

use App\Models\Rating;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class UsersManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $roleFilter = '';
    public $selectedUser = null;
    public $isEditModalOpen = false;
    public $isViewModalOpen = false;
    public $isDeleteModalOpen = false;
    public $isRatingsModalOpen = false;
    public $is_active = false;
    public $is_approved = false;
    public $ratings = [];

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedRoleFilter()
    {
        $this->resetPage();
    }

    public function getUsers()
    {
        return User::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->roleFilter, function ($query) {
                $query->where('role', $this->roleFilter);
            })
            ->with('musicianProfile')
            ->latest()
            ->paginate(10);
    }

    public function editUser($userId)
    {
        $this->selectedUser = User::with('musicianProfile')->find($userId);
        $this->is_active = $this->selectedUser->is_active ? true : false;
        $this->is_approved = $this->selectedUser->is_approved ? true : false;
        $this->isEditModalOpen = true;
    }

    public function viewUser($userId)
    {
        $this->selectedUser = User::with('musicianProfile')->find($userId);
        $this->isViewModalOpen = true;
    }

    public function confirmDelete($userId)
    {
        $this->selectedUser = User::find($userId);
        $this->isDeleteModalOpen = true;
    }

    public function deleteUser()
    {
        if ($this->selectedUser) {
            $this->selectedUser->delete();
            $this->isDeleteModalOpen = false;
            $this->dispatch(
                'notify',
                message: 'User deleted successfully',
                type: 'success',
            );
        }
    }

    public function updateUser()
    {
        if ($this->selectedUser) {
            $this->selectedUser->update([
                'is_active' => $this->is_active,
                'is_approved' => $this->is_approved,
            ]);

            $this->selectedUser->refresh();
            $this->isEditModalOpen = false;
            $this->dispatch(
                'notify',
                message: 'Status updated successfully',
                type: 'success',
            );
        }
    }

    public function viewRatings($userId)
    {
        $this->selectedUser = User::with(['musicianProfile'])->find($userId);

        if ($this->selectedUser && $this->selectedUser->musicianProfile) {
            $this->ratings = Rating::with('user')
                ->where('musician_profile_id', $this->selectedUser->musicianProfile->id)
                ->latest()
                ->get();

            $this->isRatingsModalOpen = true;
        }
    }

    public function render()
    {
        return view('livewire.admin.users-management', [
            'users' => $this->getUsers(),
        ]);
    }
}
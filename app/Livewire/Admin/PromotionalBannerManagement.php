<?php

namespace App\Livewire\Admin;

use App\Models\PromotionalBanner;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class PromotionalBannerManagement extends Component
{
    use WithPagination, WithFileUploads;

    // Search and filter properties
    public $search = '';
    public $statusFilter = '';
    public $audienceFilter = '';
    
    // Modal control properties
    public $isModalOpen = false;
    public $isDeleteModalOpen = false;
    public $modalMode = 'create'; // 'create' or 'edit'
    
    // Form properties
    public $selectedBanner = null;
    public $bannerId = null;
    public $title = '';
    public $image = null;
    public $existingImage = null;
    public $link_url = '';
    public $start_date = null;
    public $end_date = null;
    public $is_active = true;
    public $target_audience = 'all';
    
    // Preview
    public $imagePreview = null;
    
    protected $rules = [
        'title' => 'nullable|string|max:255',
        'image' => 'nullable|image|max:2048', // 2MB max
        'link_url' => 'nullable|url',
        'start_date' => 'nullable|date',
        'end_date' => 'nullable|date|after_or_equal:start_date',
        'is_active' => 'boolean',
        'target_audience' => 'required|in:all,musician,client',
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedAudienceFilter()
    {
        $this->resetPage();
    }

    public function getBanners()
    {
        return PromotionalBanner::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter !== '', function ($query) {
                if ($this->statusFilter === 'active') {
                    $query->active();
                } elseif ($this->statusFilter === 'inactive') {
                    $query->where('is_active', false);
                } elseif ($this->statusFilter === 'scheduled') {
                    $query->where('is_active', true)
                          ->where('start_date', '>', now());
                } elseif ($this->statusFilter === 'expired') {
                    $query->where('is_active', true)
                          ->where('end_date', '<', now());
                }
            })
            ->when($this->audienceFilter, function ($query) {
                $query->where('target_audience', $this->audienceFilter);
            })
            ->paginate(10);
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->modalMode = 'create';
        $this->isModalOpen = true;
    }

    public function openEditModal($bannerId)
    {
        $this->resetForm();
        $this->modalMode = 'edit';
        $this->selectedBanner = PromotionalBanner::findOrFail($bannerId);
        $this->bannerId = $this->selectedBanner->id;
        $this->title = $this->selectedBanner->title;
        $this->existingImage = $this->selectedBanner->image;
        $this->imagePreview = $this->selectedBanner->image_url;
        $this->link_url = $this->selectedBanner->link_url;
        $this->start_date = $this->selectedBanner->start_date ? $this->selectedBanner->start_date->format('Y-m-d') : null;
        $this->end_date = $this->selectedBanner->end_date ? $this->selectedBanner->end_date->format('Y-m-d') : null;
        $this->is_active = $this->selectedBanner->is_active;
        $this->target_audience = $this->selectedBanner->target_audience;
        
        $this->isModalOpen = true;
    }

    public function confirmDelete($bannerId)
    {
        $this->selectedBanner = PromotionalBanner::findOrFail($bannerId);
        $this->isDeleteModalOpen = true;
    }

    public function deleteBanner()
    {
        if ($this->selectedBanner) {
            // Delete the image file
            if ($this->selectedBanner->image) {
                Storage::disk('public')->delete('promotional_banners/' . $this->selectedBanner->image);
            }
            
            $this->selectedBanner->delete();
            $this->isDeleteModalOpen = false;
            $this->dispatch(
                'notify',
                message: 'Promotional banner deleted successfully',
                type: 'success',
            );
        }
    }

    public function saveBanner()
    {
        $this->validate();
        
        $data = [
            'title' => $this->title,
            'link_url' => $this->link_url,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'is_active' => $this->is_active,
            'target_audience' => $this->target_audience,
        ];
        
        // Handle image upload
        if ($this->image) {
            $data['image'] = uploadFile($this->image, 'promotional_banners', $this->existingImage);
        } elseif ($this->modalMode === 'create') {
            $this->validate([
                'image' => 'required|image|max:2048',
            ]);
        }
        
        if ($this->modalMode === 'create') {
            PromotionalBanner::create($data);
            $message = 'Promotional banner created successfully';
        } else {
            if (!$this->image) {
                // Keep existing image if no new one is uploaded
                unset($data['image']);
            }
            $this->selectedBanner->update($data);
            $message = 'Promotional banner updated successfully';
        }
        
        $this->isModalOpen = false;
        $this->dispatch(
            'notify',
            message: $message,
            type: 'success',
        );
    }

    public function toggleStatus($bannerId)
    {
        $banner = PromotionalBanner::findOrFail($bannerId);
        $banner->update([
            'is_active' => !$banner->is_active,
        ]);
        
        $status = $banner->is_active ? 'activated' : 'deactivated';
        $this->dispatch(
            'notify',
            message: "Banner {$status} successfully",
            type: 'success',
        );
    }

    public function resetForm()
    {
        $this->selectedBanner = null;
        $this->bannerId = null;
        $this->title = '';
        $this->image = null;
        $this->existingImage = null;
        $this->imagePreview = null;
        $this->link_url = '';
        $this->start_date = null;
        $this->end_date = null;
        $this->is_active = true;
        $this->target_audience = 'all';
        
        $this->resetErrorBag();
    }

    public function updatedImage()
    {
        $this->validate([
            'image' => 'image|max:2048',
        ]);
        
        // Create a temporary URL for preview
        $this->imagePreview = $this->image->temporaryUrl();
    }

    public function render()
    {
        return view('livewire.admin.promotional-banner-management', [
            'banners' => $this->getBanners(),
        ]);
    }
}

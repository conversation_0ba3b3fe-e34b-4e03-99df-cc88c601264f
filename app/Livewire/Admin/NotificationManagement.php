<?php

namespace App\Livewire\Admin;

use App\Models\Notification;
use Livewire\Component;
use Livewire\WithPagination;

class NotificationManagement extends Component
{
    use WithPagination;

    // Search and filter properties
    public $search = '';
    public $typeFilter = '';
    public $statusFilter = '';

    // Modal control properties
    public $isViewModalOpen = false;
    public $isDeleteModalOpen = false;
    public $isDeleteAllModalOpen = false;

    // Selected notification
    public $selectedNotification = null;

    public function mount()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedTypeFilter()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function getNotifications()
    {
        return Notification::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                        ->orWhere('body', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->typeFilter, function ($query) {
                if ($this->typeFilter === 'personal') {
                    $query->whereNotNull('user_id');
                } elseif ($this->typeFilter === 'group') {
                    $query->whereNull('user_id')->whereNotNull('notification_for');
                }
            })
            ->when($this->statusFilter, function ($query) {
                if ($this->statusFilter === 'read') {
                    $query->where('is_read', true);
                } elseif ($this->statusFilter === 'unread') {
                    $query->where('is_read', false);
                }
            })
            ->with('user')
            ->latest()
            ->paginate(10);
    }

    public function viewNotification($notificationId)
    {
        $this->selectedNotification = Notification::with('user')->find($notificationId);
        $this->isViewModalOpen = true;
    }

    public function confirmDelete($notificationId)
    {
        $this->selectedNotification = Notification::find($notificationId);
        $this->isDeleteModalOpen = true;
    }

    public function confirmDeleteAll()
    {
        $this->isDeleteAllModalOpen = true;
    }

    public function deleteNotification()
    {
        if ($this->selectedNotification) {
            $this->selectedNotification->delete();
            $this->isDeleteModalOpen = false;
            $this->dispatch(
                'notify',
                message: 'Notification deleted successfully',
                type: 'success',
            );
        }
    }

    public function deleteAllNotifications()
    {
        Notification::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                        ->orWhere('body', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->typeFilter, function ($query) {
                if ($this->typeFilter === 'personal') {
                    $query->whereNotNull('user_id');
                } elseif ($this->typeFilter === 'group') {
                    $query->whereNull('user_id')->whereNotNull('notification_for');
                }
            })
            ->when($this->statusFilter, function ($query) {
                if ($this->statusFilter === 'read') {
                    $query->where('is_read', true);
                } elseif ($this->statusFilter === 'unread') {
                    $query->where('is_read', false);
                }
            })
            ->delete();

        $this->isDeleteAllModalOpen = false;
        $this->dispatch(
            'notify',
            message: 'All matching notifications deleted successfully',
            type: 'success',
        );
    }

    public function markAsRead($notificationId)
    {
        $notification = Notification::find($notificationId);
        if ($notification) {
            $notification->markAsRead();
            $this->dispatch(
                'notify',
                message: 'Notification marked as read',
                type: 'success',
            );
        }
    }

    public function markAllAsRead()
    {
        Notification::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                        ->orWhere('body', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->typeFilter, function ($query) {
                if ($this->typeFilter === 'personal') {
                    $query->whereNotNull('user_id');
                } elseif ($this->typeFilter === 'group') {
                    $query->whereNull('user_id')->whereNotNull('notification_for');
                }
            })
            ->where('is_read', false)
            ->update(['is_read' => true]);

        $this->dispatch(
            'notify',
            message: 'All notifications marked as read',
            type: 'success',
        );
    }

    public function render()
    {
        return view('livewire.admin.notification-management', [
            'notifications' => $this->getNotifications(),
        ]);
    }
}

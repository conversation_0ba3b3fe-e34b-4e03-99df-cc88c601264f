<?php

namespace App\Livewire\Admin;

use App\Models\Notification;
use App\Models\PromotionalBanner;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Dashboard extends Component
{
    public $timeRange = 'month';
    public $chartData = [];

    public function mount()
    {
        $this->loadChartData();
    }

    public function updatedTimeRange()
    {
        $this->loadChartData();
    }

    public function loadChartData()
    {
        $days = match($this->timeRange) {
            'week' => 7,
            'month' => 30,
            'year' => 365,
            default => 30
        };

        $startDate = Carbon::now()->subDays($days);

        // Get user registrations by day
        $userRegistrations = User::where('created_at', '>=', $startDate)
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // Get revenue by day
        $revenue = Transaction::where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(amount) as amount'))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // Prepare chart data
        $labels = [];
        $registrationData = [];
        $revenueData = [];

        $format = $this->timeRange === 'year' ? 'M' : 'M d';

        for ($i = 0; $i < $days; $i++) {
            $date = Carbon::now()->subDays($days - $i - 1);
            $dateStr = $date->format('Y-m-d');
            $labels[] = $date->format($format);

            $registrationData[] = $userRegistrations->get($dateStr) ? $userRegistrations->get($dateStr)->count : 0;
            $revenueData[] = $revenue->get($dateStr) ? $revenue->get($dateStr)->amount : 0;
        }

        $this->chartData = [
            'labels' => $labels,
            'registrations' => $registrationData,
            'revenue' => $revenueData
        ];
    }

    public function getTotalUsersProperty()
    {
        return User::count();
    }

    public function getMusicianCountProperty()
    {
        return User::where('role', 'musician')->count();
    }

    public function getClientCountProperty()
    {
        return User::where('role', 'client')->count();
    }

    public function getActiveSubscriptionsProperty()
    {
        return Subscription::whereIn('status', ['active', 'trialing'])->count();
    }

    public function getMonthlyRevenueProperty()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        return Transaction::where('created_at', '>=', $startOfMonth)
            ->where('status', 'completed')
            ->sum('amount');
    }

    public function getTotalRevenueProperty()
    {
        return Transaction::where('status', 'completed')->sum('amount');
    }

    public function getActiveBannersProperty()
    {
        return PromotionalBanner::active()->count();
    }

    public function getUnreadNotificationsProperty()
    {
        return Notification::where('is_read', false)->count();
    }

    public function getNewUsersProperty()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        return User::where('created_at', '>=', $startOfMonth)->count();
    }

    public function getNewSubscriptionsProperty()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        return Subscription::where('created_at', '>=', $startOfMonth)->count();
    }

    public function getTopMusiciansProperty()
    {
        return User::where('role', 'musician')
            ->withCount(['favoriteMusicians as favorite_count' => function($query) {
                $query->where('favorite_musicians.created_at', '>=', Carbon::now()->subDays(30));
            }])
            ->orderByDesc('favorite_count')
            ->limit(5)
            ->get();
    }

    public function getRecentTransactionsProperty()
    {
        return Transaction::with('user')
            ->where('status', 'completed')
            ->latest()
            ->limit(5)
            ->get();
    }

    public function render()
    {
        return view('livewire.admin.dashboard');
    }
}

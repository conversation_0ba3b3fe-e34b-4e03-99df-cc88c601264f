<?php

namespace App\Livewire\Admin;

use App\Models\Page;
use Livewire\Component;

class PageEdit extends Component
{
    public Page $page;
    
    // Form properties
    public $title = '';
    public $content = '';
    public $is_active = true;

    public function mount(Page $page)
    {
        // Only allow editing of terms and privacy pages
        if (!in_array($page->slug, ['terms-and-conditions', 'privacy-policy'])) {
            abort(404);
        }
        
        $this->page = $page;
        $this->title = $page->title;
        $this->content = $page->content;
        $this->is_active = $page->is_active;
    }

    public function save()
    {
        $this->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'is_active' => 'boolean',
        ]);

        try {
            $this->page->update([
                'title' => $this->title,
                'content' => $this->content,
                'is_active' => $this->is_active,
            ]);

            $this->dispatch(
                'notify',
                message: 'Page updated successfully',
                type: 'success',
            );

        } catch (\Exception $e) {
            $this->dispatch(
                'notify',
                message: 'An error occurred while saving the page.',
                type: 'error',
            );
        }
    }

    public function goBack()
    {
        return $this->redirect(route('admin.pages.index'));
    }

    public function render()
    {
        return view('livewire.admin.page-edit')
            ->layout('components.layouts.app', ['title' => 'Edit ' . $this->page->title]);
    }
}

<?php

namespace App\Livewire\Admin;

use App\Models\Page;
use Livewire\Component;

class PageManagement extends Component
{
    // Search property
    public $search = '';

    public function updatedSearch()
    {
        // Reset search when updated
    }

    public function editPage($pageId)
    {
        return $this->redirect(route('admin.pages.edit', $pageId));
    }

    public function toggleStatus($pageId)
    {
        try {
            $page = Page::findOrFail($pageId);
            $page->update(['is_active' => !$page->is_active]);

            $status = $page->is_active ? 'activated' : 'deactivated';
            $this->dispatch(
                'notify',
                message: "Page {$status} successfully",
                type: 'success',
            );

        } catch (\Exception $e) {
            $this->dispatch(
                'notify',
                message: 'An error occurred while updating the page status.',
                type: 'error',
            );
        }
    }

    public function render()
    {
        // Only show the two seeded pages: terms-and-conditions and privacy-policy
        $pages = Page::query()
            ->whereIn('slug', ['terms-and-conditions', 'privacy-policy'])
            ->when($this->search, function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('slug', 'like', '%' . $this->search . '%');
            })
            ->orderBy('slug')
            ->get();

        return view('livewire.admin.page-management', compact('pages'))
            ->layout('components.layouts.app', ['title' => 'Page Management']);
    }
}

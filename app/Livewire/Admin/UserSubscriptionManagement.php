<?php

namespace App\Livewire\Admin;

use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;
use Stripe\Stripe;
use Stripe\StripeClient;

class UserSubscriptionManagement extends Component
{
    use WithPagination;

    // Search and filter properties
    public $search = '';
    public $statusFilter = '';
    public $userTypeFilter = '';
    public $dateRangeFilter = '';

    // Modal control properties
    public $isViewModalOpen = false;
    public $isEditModalOpen = false;
    public $isDeleteModalOpen = false;
    public $isRefundModalOpen = false;

    // Cancellation options
    public $cancelAtPeriodEndOption = true;

    // Selected subscription and transaction
    public $selectedSubscription = null;
    public $selectedTransaction = null;

    // Form properties
    public $subscriptionId;
    public $userId;
    public $userName;
    public $userEmail;
    public $planName;
    public $status;
    public $trialEndsAt;
    public $endsAt;
    public $nextBillingDate;
    public $cancelAtPeriodEnd = false;

    // Refund properties
    public $refundAmount;
    public $refundReason = '';
    public $fullRefund = true;

    // Stripe client
    protected $stripe;

    public function mount()
    {
        $this->resetPage();
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $this->stripe = new StripeClient(env('STRIPE_SECRET'));
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function updatedUserTypeFilter()
    {
        $this->resetPage();
    }

    public function updatedDateRangeFilter()
    {
        $this->resetPage();
    }

    public function getSubscriptions()
    {
        return Subscription::query()
            ->with(['user', 'plan', 'transactions'])
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%');
                })
                    ->orWhereHas('plan', function ($q) {
                        $q->where('name', 'like', '%' . $this->search . '%');
                    });
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->when($this->userTypeFilter, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('role', $this->userTypeFilter);
                });
            })
            ->when($this->dateRangeFilter, function ($query) {
                if ($this->dateRangeFilter === 'today') {
                    $query->whereDate('created_at', Carbon::today());
                } elseif ($this->dateRangeFilter === 'week') {
                    $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                } elseif ($this->dateRangeFilter === 'month') {
                    $query->whereMonth('created_at', Carbon::now()->month)
                        ->whereYear('created_at', Carbon::now()->year);
                } elseif ($this->dateRangeFilter === 'year') {
                    $query->whereYear('created_at', Carbon::now()->year);
                }
            })
            ->latest()
            ->paginate(10);
    }

    public function viewSubscription($subscriptionId)
    {
        $this->selectedSubscription = Subscription::with(['user', 'plan', 'transactions'])->findOrFail($subscriptionId);
        $this->isViewModalOpen = true;
    }

    public function openEditModal($subscriptionId)
    {
        $this->resetForm();
        $this->selectedSubscription = Subscription::with(['user', 'plan'])->findOrFail($subscriptionId);

        $this->subscriptionId = $this->selectedSubscription->id;
        $this->userId = $this->selectedSubscription->user_id;
        $this->userName = $this->selectedSubscription->user->name;
        $this->userEmail = $this->selectedSubscription->user->email;
        $this->planName = $this->selectedSubscription->plan->name;
        $this->status = $this->selectedSubscription->status;
        $this->trialEndsAt = $this->selectedSubscription->trial_ends_at ? $this->selectedSubscription->trial_ends_at->format('Y-m-d') : null;
        $this->endsAt = $this->selectedSubscription->ends_at ? $this->selectedSubscription->ends_at->format('Y-m-d') : null;
        $this->nextBillingDate = $this->selectedSubscription->next_billing_date ? $this->selectedSubscription->next_billing_date->format('Y-m-d') : null;
        $this->cancelAtPeriodEnd = $this->selectedSubscription->cancel_at_period_end;

        $this->isEditModalOpen = true;
    }

    public function updateSubscription()
    {
        $this->validate([
            'status' => 'required|in:active,trialing,canceled,expired,past_due,unpaid',
            'trialEndsAt' => 'nullable|date',
            'endsAt' => 'nullable|date',
            'nextBillingDate' => 'nullable|date',
            'cancelAtPeriodEnd' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $subscription = Subscription::findOrFail($this->subscriptionId);

            // Update Stripe subscription if it exists
            if ($subscription->stripe_subscription_id) {
                // Only update cancel_at_period_end in Stripe
                if ($this->cancelAtPeriodEnd !== $subscription->cancel_at_period_end) {
                    // Ensure Stripe is initialized
                    if (!$this->stripe) {
                        Stripe::setApiKey(env('STRIPE_SECRET'));
                        $this->stripe = new StripeClient(env('STRIPE_SECRET'));
                    }

                    $this->stripe->subscriptions->update($subscription->stripe_subscription_id, [
                        'cancel_at_period_end' => $this->cancelAtPeriodEnd
                    ]);
                }
            }

            // Update local subscription
            $subscription->update([
                'status' => $this->status,
                'trial_ends_at' => $this->trialEndsAt ? Carbon::parse($this->trialEndsAt) : null,
                'ends_at' => $this->endsAt ? Carbon::parse($this->endsAt) : null,
                'next_billing_date' => $this->nextBillingDate ? Carbon::parse($this->nextBillingDate) : null,
                'cancel_at_period_end' => $this->cancelAtPeriodEnd,
            ]);

            DB::commit();

            $this->isEditModalOpen = false;
            $this->dispatch(
                'notify',
                message: 'Subscription updated successfully',
                type: 'success',
            );
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error updating subscription: ' . $e->getMessage());
            $this->dispatch(
                'notify',
                message: 'Failed to update subscription: ' . $e->getMessage(),
                type: 'error',
            );
        }
    }

    public function confirmCancel($subscriptionId)
    {
        $this->selectedSubscription = Subscription::findOrFail($subscriptionId);
        $this->isDeleteModalOpen = true;
    }

    public function cancelSubscription()
    {
        try {
            DB::beginTransaction();

            $subscription = $this->selectedSubscription;

            // For one-time payments, just mark as canceled
            if (!$subscription->stripe_subscription_id) {
                $subscription->update([
                    'status' => 'canceled',
                    'ends_at' => Carbon::now(),
                ]);
            } else {
                // For recurring subscriptions, handle based on cancellation option
                // Ensure Stripe is initialized
                if (!$this->stripe) {
                    Stripe::setApiKey(env('STRIPE_SECRET'));
                    $this->stripe = new StripeClient(env('STRIPE_SECRET'));
                }

                if ($this->cancelAtPeriodEndOption) {
                    // Cancel at period end
                    $this->stripe->subscriptions->update($subscription->stripe_subscription_id, [
                        'cancel_at_period_end' => true
                    ]);

                    // Update local subscription
                    $subscription->update([
                        'cancel_at_period_end' => true,
                        // Status remains active until the period ends
                    ]);

                    $message = 'Subscription will be canceled at the end of the billing period';
                } else {
                    // Cancel immediately
                    $this->stripe->subscriptions->cancel($subscription->stripe_subscription_id);

                    // Update local subscription
                    $subscription->update([
                        'status' => 'canceled',
                        'cancel_at_period_end' => false,
                        'ends_at' => Carbon::now(),
                    ]);

                    $message = 'Subscription canceled immediately';
                }
            }

            DB::commit();

            $this->isDeleteModalOpen = false;
            $this->dispatch(
                'notify',
                message: $subscription->stripe_subscription_id
                    ? $message
                    : 'Subscription canceled successfully',
                type: 'success',
            );
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error canceling subscription: ' . $e->getMessage());
            $this->dispatch(
                'notify',
                message: 'Failed to cancel subscription: ' . $e->getMessage(),
                type: 'error',
            );
        }
    }

    public function openRefundModal($transactionId)
    {
        $this->selectedTransaction = Transaction::with(['subscription', 'user'])->findOrFail($transactionId);
        $this->refundAmount = $this->selectedTransaction->amount;
        $this->fullRefund = true;
        $this->refundReason = '';
        $this->isRefundModalOpen = true;
    }

    public function updatedFullRefund()
    {
        if ($this->fullRefund) {
            $this->refundAmount = $this->selectedTransaction->amount;
        }
    }

    public function processRefund()
    {
        $this->validate([
            'refundAmount' => 'required|numeric|min:0.01|max:' . $this->selectedTransaction->amount,
            'refundReason' => 'required|string|min:3',
        ]);

        try {
            // Ensure Stripe is initialized
            if (!$this->stripe) {
                Stripe::setApiKey(env('STRIPE_SECRET'));
                $this->stripe = new StripeClient(env('STRIPE_SECRET'));
            }

            DB::beginTransaction();

            $transaction = $this->selectedTransaction;

            // Process refund in Stripe
            if ($transaction->transaction_id) {
                // Check if the transaction ID is an invoice ID (starts with 'in_')
                if (strpos($transaction->transaction_id, 'in_') === 0) {
                    // Retrieve the invoice to get the payment intent
                    $invoice = $this->stripe->invoices->retrieve($transaction->transaction_id);

                    if (!$invoice->payment_intent) {
                        throw new Exception('No payment intent found for this invoice');
                    }

                    // Use the payment intent ID from the invoice
                    $paymentIntentId = $invoice->payment_intent;
                } else {
                    // Use the transaction ID directly (assuming it's a payment intent or charge ID)
                    $paymentIntentId = $transaction->transaction_id;
                }

                $refund = $this->stripe->refunds->create([
                    'payment_intent' => $paymentIntentId,
                    'amount' => $this->fullRefund ? null : (int)($this->refundAmount * 100), // Convert to cents if partial
                    'reason' => 'requested_by_customer',
                    'metadata' => [
                        'reason' => $this->refundReason,
                        'refunded_by' => 'admin',
                    ],
                ]);

                // Create a new transaction record for the refund
                Transaction::create([
                    'user_id' => $transaction->user_id,
                    'subscription_id' => $transaction->subscription_id,
                    'payment_method' => 'stripe',
                    'transaction_id' => $refund->id,
                    'amount' => -$this->refundAmount, // Negative amount for refund
                    'currency' => $transaction->currency,
                    'status' => 'refunded',
                    'description' => "Refund for transaction #{$transaction->id}: {$this->refundReason}",
                    'metadata' => [
                        'original_transaction_id' => $transaction->id,
                        'reason' => $this->refundReason,
                    ],
                ]);

                // Update original transaction status
                $transaction->update([
                    'status' => $this->fullRefund ? 'refunded' : 'partially_refunded',
                    'metadata' => array_merge($transaction->metadata ?? [], [
                        'refund_id' => $refund->id,
                        'refund_amount' => $this->refundAmount,
                        'refund_reason' => $this->refundReason,
                    ]),
                ]);

                // If full refund and subscription is active, cancel it
                if ($this->fullRefund && $transaction->subscription && $transaction->subscription->isActive()) {
                    $subscription = $transaction->subscription;

                    if ($subscription->stripe_subscription_id) {
                        $this->stripe->subscriptions->cancel($subscription->stripe_subscription_id);
                    }

                    $subscription->update([
                        'status' => 'canceled',
                        'ends_at' => Carbon::now(),
                    ]);
                }
            }

            DB::commit();

            $this->isRefundModalOpen = false;
            $this->dispatch(
                'notify',
                message: 'Refund processed successfully',
                type: 'success',
            );
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error processing refund: ' . $e->getMessage());
            $this->dispatch(
                'notify',
                message: 'Failed to process refund: ' . $e->getMessage(),
                type: 'error',
            );
        }
    }

    public function resetForm()
    {
        $this->selectedSubscription = null;
        $this->selectedTransaction = null;
        $this->subscriptionId = null;
        $this->userId = null;
        $this->userName = '';
        $this->userEmail = '';
        $this->planName = '';
        $this->status = '';
        $this->trialEndsAt = null;
        $this->endsAt = null;
        $this->nextBillingDate = null;
        $this->cancelAtPeriodEnd = false;
        $this->cancelAtPeriodEndOption = true; // Reset to default (cancel at period end)
        $this->refundAmount = 0;
        $this->refundReason = '';
        $this->fullRefund = true;

        $this->resetErrorBag();
    }

    public function render()
    {
        return view('livewire.admin.user-subscription-management', [
            'subscriptions' => $this->getSubscriptions(),
        ]);
    }
}

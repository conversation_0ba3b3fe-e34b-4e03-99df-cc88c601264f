<?php

namespace App\Livewire;

use App\Models\Page;
use Livewire\Component;

class TermsAndConditions extends Component
{
    public $page;

    public function mount()
    {
        $this->page = Page::getBySlug('terms-and-conditions');
        
        if (!$this->page) {
            abort(404, 'Terms and Conditions page not found');
        }
    }

    public function render()
    {
        return view('livewire.terms-and-conditions')->layout('components.layouts.site', [
            'title' => $this->page->title
        ]);
    }
}

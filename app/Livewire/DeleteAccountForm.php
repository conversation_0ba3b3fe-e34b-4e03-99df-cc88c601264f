<?php

namespace App\Livewire;

use App\Mail\DeleteAccountRequestMail;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class DeleteAccountForm extends Component
{
    public $name = '';
    public $email = '';
    public $reason = '';
    public $additional_feedback = '';

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'reason' => 'required|string',
        'additional_feedback' => 'nullable|string|max:1000',
    ];

    protected $messages = [
        'name.required' => 'Please enter your name.',
        'email.required' => 'Please enter your email address.',
        'email.email' => 'Please enter a valid email address.',
        'reason.required' => 'Please select a reason for deletion.',
        'additional_feedback.max' => 'Additional feedback cannot exceed 1000 characters.',
    ];

    public function submit()
    {
        $this->validate();

        try {
            $deleteData = [
                'name' => $this->name,
                'email' => $this->email,
                'reason' => $this->reason,
                'additional_feedback' => $this->additional_feedback,
                'submitted_at' => now()->format('Y-m-d H:i:s'),
            ];

            // Send email to admin
            Mail::to(env('ADMIN_EMAIL'))->send(new DeleteAccountRequestMail($deleteData));

            // Reset form
            $this->reset(['name', 'email', 'reason', 'additional_feedback']);

            // Show success message
            $this->dispatch(
                'notify',
                message: 'Your account deletion request has been submitted. We will process it within 24-48 hours.',
                type: 'success',
            );
        } catch (\Exception $e) {
            $this->dispatch(
                'notify',
                message: 'Sorry, there was an error submitting your request. Please try again.',
                type: 'error',
            );
        }
    }

    public function getReasonOptions()
    {
        return [
            'Privacy Concerns',
            'Not Using the Service',
            'Found Alternative Service',
            'Technical Issues',
            'Cost Concerns',
            'Other',
        ];
    }

    public function render()
    {
        return view('livewire.delete-account-form')->layout('components.layouts.auth', [
            'title' => 'Delete Account Request',
            'description' => 'Let us know why you want to delete your account.',
        ]);
    }
}

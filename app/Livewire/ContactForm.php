<?php

namespace App\Livewire;

use App\Mail\ContactMail;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class ContactForm extends Component
{
    public $name = '';
    public $email = '';
    public $subject = '';
    public $message = '';

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'subject' => 'required|string|max:255',
        'message' => 'required|string|max:2000',
    ];

    protected $messages = [
        'name.required' => 'Please enter your name.',
        'email.required' => 'Please enter your email address.',
        'email.email' => 'Please enter a valid email address.',
        'subject.required' => 'Please enter a subject.',
        'message.required' => 'Please enter your message.',
        'message.max' => 'Message cannot exceed 2000 characters.',
    ];

    public function submit()
    {
        $this->validate();

        try {
            $contactData = [
                'name' => $this->name,
                'email' => $this->email,
                'subject' => $this->subject,
                'message' => $this->message,
                'submitted_at' => now()->format('Y-m-d H:i:s'),
            ];

            // Send email to admin
            Mail::to(env('ADMIN_EMAIL'))->send(new ContactMail($contactData));

            // Reset form
            $this->reset(['name', 'email', 'subject', 'message']);

            // Show success message
            $this->dispatch(
                'notify',
                message : 'Thank you for contacting us! We will get back to you soon.',
                type : 'success',
            );

        } catch (\Exception $e) {
            $this->dispatch(
                'notify',
                message : 'Sorry, there was an error sending your message. Please try again.',
                type : 'error',
            );
        }
    }

    public function render()
    {
        return view('livewire.contact-form')->layout('components.layouts.auth', [
            'title' => 'Contact Us',
            'description' => 'Get in touch with our team. We\'d love to hear from you!',
        ]);
    }
}

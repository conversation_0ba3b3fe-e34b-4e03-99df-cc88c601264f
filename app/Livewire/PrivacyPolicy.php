<?php

namespace App\Livewire;

use App\Models\Page;
use Livewire\Component;

class PrivacyPolicy extends Component
{
    public $page;

    public function mount()
    {
        $this->page = Page::getBySlug('privacy-policy');
        
        if (!$this->page) {
            abort(404, 'Privacy Policy page not found');
        }
    }

    public function render()
    {
        return view('livewire.privacy-policy')->layout('components.layouts.site', [
            'title' => $this->page->title
        ]);
    }
}

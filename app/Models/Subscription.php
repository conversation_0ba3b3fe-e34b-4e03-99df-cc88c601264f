<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    protected  $appends = ['formatted_next_billing_date','formatted_trial_ends_at', 'formatted_ends_at'];

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'stripe_price_id',
        'status',
        'trial_ends_at',
        'ends_at',
        'next_billing_date',
        'cancel_at_period_end',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'ends_at' => 'datetime',
        'next_billing_date' => 'datetime',
        'cancel_at_period_end' => 'boolean',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the plan that the subscription belongs to.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the transactions for the subscription.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Determine if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' || $this->status === 'trialing';
    }

    /**
     * Determine if the subscription is canceled.
     */
    public function isCanceled(): bool
    {
        return $this->status === 'canceled';
    }

    /**
     * Determine if the subscription is on trial.
     */
    public function onTrial(): bool
    {
        return $this->status === 'trialing';
    }

    /**
     * Determine if the subscription has expired.
     */
    public function hasExpired(): bool
    {
        return $this->status === 'expired';
    }

    /**
     * Accessor for formatted_next_billing_date.
     */
    public function getFormattedNextBillingDateAttribute()
    {
        return $this->next_billing_date ? $this->next_billing_date->format('M d, Y') : null;
    }

    /**
     * Accessor for formatted_trial_ends_at.
     */
    public function getFormattedTrialEndsAtAttribute()
    {
        return $this->trial_ends_at ? $this->trial_ends_at->format('M d, Y') : null;
    }

    /**
     * Accessor for formatted_ends_at.
     */
    public function getFormattedEndsAtAttribute()
    {
        return $this->ends_at ? $this->ends_at->format('M d, Y') : null;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Chat extends Model
{
    protected $fillable = [
        'client_id',
        'musician_id',
    ];

    public function client()
    {
        return $this->belongsTo(User::class, 'client_id', 'id');
    }
    public function musician()
    {
        return $this->belongsTo(User::class, 'musician_id', 'id');
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }
}

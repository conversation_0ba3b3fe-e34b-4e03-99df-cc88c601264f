<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    protected $appends = ['image_url', 'has_active_subscription'];

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'brand_name',
        'email',
        'password',
        'role',
        'fcm_token',
        'image',
        'is_verified',
        'is_active',
        'is_approved',
        'is_profile_completed',
        'is_vip_user',
        'longitude',
        'latitude',
        'availability',
        'live_location',
        'is_live'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->map(fn(string $name) => Str::of($name)->substr(0, 1))
            ->implode('');
    }

    public function getImageUrlAttribute()
    {
        return $this->image ? url('storage/profile_photos/' . $this->image) : null;
    }

    public function musicianProfile()
    {
        return $this->hasOne(MusicianProfile::class);
    }

    public function favoriteMusicians()
    {
        return $this->hasMany(FavoriteMusician::class, 'user_id');
    }

    public function favoritedByUsers()
    {
        return $this->hasMany(FavoriteMusician::class, 'musician_id');
    }

    /**
     * Get the user's latest subscription.
     */
    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->latest();
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->whereIn('status', ['active', 'trialing'])->latest();
    }

    /**
     * Get all subscriptions for the user.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get all transactions for the user.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the user's has_active_subscription attribute.
     */
    public function getHasActiveSubscriptionAttribute(): bool
    {
        return $this->activeSubscription()->exists() || $this->is_vip_user;
    }


    /**
     * Check if the user is on a trial subscription.
     */
    public function onTrial(): bool
    {
        $subscription = $this->activeSubscription;
        return $subscription && $subscription->status === 'trialing';
    }
}

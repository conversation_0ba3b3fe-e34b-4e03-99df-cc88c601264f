<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MusicianGallery extends Model
{
    protected $fillable = [
        'musician_profile_id',
        'image',
    ];

    protected $appends = ['image_url'];

    /**
     * Get the gallery image URL
     */
    public function getImageUrlAttribute()
    {
        return $this->image ? url('storage/musician_gallery/' . $this->image) : null;
    }

    /**
     * Get the musician profile that owns the gallery image
     */
    public function musicianProfile(): BelongsTo
    {
        return $this->belongsTo(MusicianProfile::class);
    }
}

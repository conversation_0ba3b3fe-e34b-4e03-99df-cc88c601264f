<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $appends = ['formatted_created_at'];

    protected $fillable = [
        'title',
        'body',
        'is_read',
        'user_id',
        'notification_for',
        'data',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'data' => 'array',
    ];

    /**
     * Get the user that owns the notification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include unread notifications.
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope a query to only include notifications for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('user_id', $userId)
              ->orWhere(function ($q) use ($userId) {
                  $user = User::find($userId);
                  if ($user) {
                      $q->whereNull('user_id')
                        ->where(function ($q) use ($user) {
                            $q->where('notification_for', 'all')
                              ->orWhere('notification_for', $user->role);
                        });
                  }
              });
        });
    }

    /**
     * Mark the notification as read.
     */
    public function markAsRead()
    {
        $this->update(['is_read' => true]);
    }

    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at ? $this->created_at->diffForHumans() : null;
    }
}

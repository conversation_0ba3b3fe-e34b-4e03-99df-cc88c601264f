<?php

namespace App\Traits;

use App\Models\Message;
use App\Models\Notification;
use App\Models\User;
use Kreait\Firebase\Contract\Database as FirebaseDatabase;

trait FirebaseDatabaseTrait
{
    /**
     * Update unread counts in Firebase for a user
     *
     * @param string $type Type of count (chat)
     * @param int $userId User ID
     * @param int $chatId Chat ID (optional)
     * @return void
     */
    public function updateUnreadCount($type, $userId, $chatId = null)
    {
        try {
            // Base path for user's unread counts
            $basePath = 'unread_counts/' . $userId;

            $data = null;
            if ($type == 'chat') {
                $count = Message::where('chat_id', $chatId)
                    ->where('is_read', 0)
                    ->where('sender_id', '!=', $userId)
                    ->count();

                $lastMessage = Message::where('chat_id', $chatId)
                    ->latest()
                    ->first(['id', 'message', 'created_at']);
                $data = [
                    'count' => $count,
                    'last_message' => $lastMessage ? $lastMessage->toArray() : null,
                ];
            } elseif ($type == 'unread_notifications') {
                $userRole = User::find($userId)?->role;
                $count = Notification::where(function ($query) use ($userId, $userRole) {
                    $query->where('user_id', $userId)
                        ->orWhere(function ($q) use ($userRole) {
                            $q->whereNull('user_id')
                                ->where(function ($q) use ($userRole) {
                                    $q->where('notification_for', 'all')
                                        ->orWhere('notification_for', $userRole);
                                });
                        });
                })->count();

                \Log::info('Unread notifications count: ' . $count . ' for user ' . $userId);
                $data =  $count;
            }

            // Update counts in Firebase
            $firebaseDatabase = app(FirebaseDatabase::class);
            $firebaseDatabase->getReference($basePath . '/' . $type)
                ->set($data);
        } catch (\Exception $e) {
            \Log::error('Failed to update unread counts in Firebase: ' . $e->getMessage());
        }
    }
}
